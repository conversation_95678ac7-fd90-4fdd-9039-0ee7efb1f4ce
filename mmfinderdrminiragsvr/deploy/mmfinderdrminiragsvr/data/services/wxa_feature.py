# -*- coding: utf-8 -*-
import sys
import os
# 获取当前文件的目录，然后构建相对路径
current_dir = os.path.dirname(os.path.abspath(__file__))
proto_path = os.path.join(current_dir, "..", "proto", "mmbizwxaaiagententrance")
sys.path.append(proto_path)

import asyncio
import py3meshkit
from mmbiz.mmbizwxaapp.mmbizwxaaiagent.mmbizwxaaiagententrance.mmbizwxaaiagententrance_pb2 import GetWxaUsageRecordReq, GetWxaUsageRecordResp

async def test():
    req = GetWxaUsageRecordReq()
    req.useruin = 3392687165
    req.condition = 1
    req.scene = 2
    client = py3meshkit.SvrkitClient("11.141.164.58", 29077)
    # client = py3meshkit.SvrkitClient("mmbizwxaaiagententrance")
    # client = py3meshkit.MeshClient("mmbizwxaaiagententrance", mesh_listen_path="*************:8341")
    # 在创建SvrkitClient时，常用的命名参数有
    # idc_name 用于手动指定路由idc，例如 SvrkitClient("mmsearchqp", idc_name="shenzhen")
    # route_tag 用于指定路由标签，常用于动态环境测试
    # by_set 用于指定使用by_set路由模式，默认False
    # client = py3meshkit.SvrkitClient("*************", 12473)   # 当不需要路由时，可直接指定模块所在的IP、端口
    rsp = GetWxaUsageRecordResp()
    ret = await client.request(3392687165, 2, req, rsp)
    await client.close()
    json_str = py3meshkit.message_to_json(rsp)

    print(f"{json_str}")


if __name__ == "__main__":
    asyncio.run(test())