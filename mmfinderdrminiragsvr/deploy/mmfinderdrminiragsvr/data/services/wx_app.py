# -*- coding: utf-8 -*-
import sys

sys.path.append("/home/<USER>/mmfinderdrminiragsvr/so")
import logging

import minirag
from models.wx_app import GetWxAppFeatureResponse, GetWxAppUsageRecordResponse


class WxAppService:
    @staticmethod
    def get_wx_app_usage_record(uin):
        resp = minirag.get_wx_app_usage_record(uin)
        if resp.get("ret") != 0:
            logging.error(
                f"get wx app usage record failed, ret: {resp.get('ret')}, resp: {resp.get('resp')}"
            )
            return GetWxAppUsageRecordResponse(
                commuse_list=[], history_list=[], star_list=[]
            )
        logging.info(f"get wx app usage record success, resp: {resp.get('resp')}")
        return GetWxAppUsageRecordResponse(
            commuse_list=resp.get("commuse_list", []),
            history_list=resp.get("history_list", []),
            star_list=resp.get("star_list", []),
        )

    @staticmethod
    def get_wx_app_feature(uin, appid_list):
        resp = minirag.get_wx_app_feature(uin, appid_list)
        if resp.get("ret") != 0:
            logging.error(
                f"get wx app feature failed, ret: {resp.get('ret')}, resp: {resp.get('resp')}"
            )
            return GetWxAppFeatureResponse(wxa_feature_list=[])
        logging.info(f"get wx app feature success, resp: {resp.get('resp')}")
        return GetWxAppFeatureResponse(
            wxa_feature_list=resp.get("wxa_feature_list", [])
        )


if __name__ == "__main__":
    try:
        records = minirag.get_wx_app_usage_record(3392687165)
        logging.info(f"records: {records}")

        features = minirag.get_wx_app_feature(
            3392687165, ["wxaf35009675aa0b2a", "wx2c348cf579062e56"]
        )
        logging.info(f"features: {features}")

    except Exception as e:
        print(e)
