import asyncio

from fastapi import APIRouter
from models.response import StandardResponse
from models.wx_app import (
    GetWxAppFeatureRequest,
    GetWxAppFeatureResponse,
    GetWxAppUsageRecordRequest,
    GetWxAppUsageRecordResponse,
)
from services.wx_app import WxAppService

router = APIRouter()


@router.post(
    "/v1/get_usage_record", response_model=StandardResponse[GetWxAppUsageRecordResponse]
)
async def get_wx_app_usage_record(request: GetWxAppUsageRecordRequest):
    records = await asyncio.to_thread(WxAppService.get_wx_app_usage_record, request.uin)
    return StandardResponse(data=records)


@router.post(
    "/v1/get_feature", response_model=StandardResponse[GetWxAppFeatureResponse]
)
async def get_wx_app_feature(request: GetWxAppFeatureRequest):
    features = await asyncio.to_thread(
        WxAppService.get_wx_app_feature, request.uin, request.appid_list
    )
    return StandardResponse(data=features)


@router.post(
    "/v2/get_usage_record", response_model=StandardResponse[GetWxAppUsageRecordResponse]
)
async def get_wx_app_usage_record_v2(request: GetWxAppUsageRecordRequest):
    records = await asyncio.to_thread(WxAppService.get_wx_app_usage_record, request.uin)
    return StandardResponse(data=records)


@router.post(
    "/v2/get_feature", response_model=StandardResponse[GetWxAppFeatureResponse]
)
async def get_wx_app_feature_v2(request: GetWxAppFeatureRequest):
    features = await asyncio.to_thread(
        WxAppService.get_wx_app_feature, request.uin, request.appid_list
    )
    return StandardResponse(data=features)
