import logging
from contextlib import asynccontextmanager

from controllers import tool, wx_app
from fastapi import FastAPI
from logs import ilog


@asynccontextmanager
async def lifespan(app: FastAPI):
    ilog.setup_logging()
    logging.info("lifespan start")
    yield
    logging.info("lifespan end")


app = FastAPI(lifespan=lifespan)


@app.get("/health")
async def health():
    return {"status": "OK"}


app.include_router(wx_app.router, prefix="/wx_app")
app.include_router(tool.router, prefix="/tool")

if __name__ == "__main__":
    import sys

    import uvicorn

    uvicorn.run(app, host="0.0.0.0", port=int(sys.argv[1]))
