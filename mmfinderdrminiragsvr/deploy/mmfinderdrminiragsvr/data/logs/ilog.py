# -*- coding:utf-8 -*-
import logging
import sys

from svrkit.core.log import COMM_LOG_ALL, GetCommLogLevel, Handler, LogDefault

formatter = logging.Formatter(
    "%(asctime)s - %(name)s - %(levelname)s - [%(filename)s:%(lineno)d] - %(message)s"
)


class HandlerAdapter(Handler):
    def __init__(
        self,
        module_name,
        log_level,
        path="/home/<USER>/log",
        global_svr_attr_id=0,
        attach_module_shm=False,
        open_net_log=False,
        max_size=1600,
        max_line_size=4096,
        net_log_report_dye_log_only=False,
        net_log_storage_type=0,
    ):
        super().__init__(
            module_name,
            log_level,
            path=path,
            global_svr_attr_id=global_svr_attr_id,
            attach_module_shm=attach_module_shm,
            open_net_log=open_net_log,
            max_size=max_size,
            max_line_size=max_line_size,
            net_log_report_dye_log_only=net_log_report_dye_log_only,
            net_log_storage_type=net_log_storage_type,
        )

    def emit(self, record):
        LogDefault(GetCommLogLevel(record.levelno), formatter.format(record))

    def close(self):
        Handler.close(self)


def setup_logging():
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.INFO)
    handler = HandlerAdapter(
        "mmfinderdrminiragsvr",
        COMM_LOG_ALL,
        "/home/<USER>/log",
        global_svr_attr_id=111424,
        net_log_storage_type=1,
        attach_module_shm=True,
        open_net_log=True,
    )
    handler.level = logging.INFO
    root_logger.addHandler(handler)

    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(formatter)
    root_logger.addHandler(console_handler)
