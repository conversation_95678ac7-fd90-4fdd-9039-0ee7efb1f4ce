syntax = "proto2";

import "comm2/tlvpickle/skbuiltintype.proto";
package mmbizwxaaiagententrance;

message StreamRespTestReq {
  optional string input = 1;
}

message StreamRespTestResp {
  optional string output = 1;
}

message StreamRespWxChatReq {
  optional uint32 useruin = 1;
  optional string asr_text = 2;
  optional bytes bypass = 3;
}

message StreamRespWxChatResp {
  optional string model_result = 1;
  optional string tts_text = 2;
  optional string bypass = 3;
  optional uint32 cmd = 4;
  optional string notify_client_msg_json = 5;
}

message NotifyClientMsg {
  optional uint32 display_result = 1;
}

message WxaAppItem {
  optional string username = 1;
  optional uint32 update_time = 2;
  optional uint32 version_type = 3;
  optional string app_desc = 4;
  optional bool is_from_outer = 5;
}

message GetWxaUsageRecordReq {
  optional uint32 useruin = 1;
  optional uint32 condition = 10;
  optional uint32 max_updatetime = 11;
  optional uint32 history_count = 12 [default = 50];
  optional uint32 star_count = 20 [default = 20];
  optional uint32 is_show_host_appinfo = 99;
}

message GetWxaUsageRecordResp {
  repeated .mmbizwxaaiagententrance.WxaAppItem history_list = 1;
  repeated .mmbizwxaaiagententrance.WxaAppItem star_list = 2;
  optional .mmbizwxaaiagententrance.CommUseAppInfo commuse_appinfo = 3;
}

message GetWxaCommUseRecordReq {
  optional uint32 useruin = 1;
}

message GetWxaCommUseRecordResp {
  optional .mmbizwxaaiagententrance.CommUseAppInfo profile = 1;
}

message CommUseAppInfo {
  message CommUseConfig {
    message ReferItem {
      optional uint32 refer_id = 1;
      optional string zh_cn = 2;
    }
    repeated .mmbizwxaaiagententrance.CommUseAppInfo.CommUseConfig.ReferItem refer_info = 1;
    optional uint32 show_type = 2;
    optional uint32 history_show_type = 3;
    optional bool refresh_immediately = 4;
    optional uint32 no_response_ms_when_refresh = 5;
    optional string next_req_extra_data = 6;
    optional uint32 update_loading_wait_ms = 7;
    optional bool report_ssi = 8;
    optional bool report_location = 9;
    optional uint32 get_localtion_wait_ms = 10;
    optional string overview_extra_data = 11;
    optional uint32 next_req_interval_s = 12;
  }
  repeated .mmbizwxaaiagententrance.WxaAppItem common_use = 1;
  optional .mmbizwxaaiagententrance.CommUseAppInfo.CommUseConfig commuse_config = 2;
}

message CommUseProfile {
  repeated .mmbizwxaaiagententrance.CommUseAppInfo app_location = 1;
  repeated .mmbizwxaaiagententrance.CommUseAppInfo app_backup = 2;
  optional uint32 update_time = 3;
  repeated .mmbizwxaaiagententrance.CommUseAppInfo app_wxasearch = 4;
}

service MMBizWxaAiAgentEntrance {
  option (.tlvpickle.Magic) = 29077;
  rpc StreamRespWxChat(.mmbizwxaaiagententrance.StreamRespWxChatReq) returns (stream .mmbizwxaaiagententrance.StreamRespWxChatResp) {
    option (.tlvpickle.CmdID) = 1;
    option (.tlvpickle.OptString) = "u:s:";
    option (.tlvpickle.Usage) = "-u <uin> -s <str>";
  }
  rpc GetWxaUsageRecord(.mmbizwxaaiagententrance.GetWxaUsageRecordReq) returns (.mmbizwxaaiagententrance.GetWxaUsageRecordResp) {
    option (.tlvpickle.CmdID) = 2;
    option (.tlvpickle.OptString) = "";
    option (.tlvpickle.Usage) = "";
  }
  rpc StreamRespTest(.mmbizwxaaiagententrance.StreamRespTestReq) returns (stream .mmbizwxaaiagententrance.StreamRespTestResp) {
    option (.tlvpickle.CmdID) = 20;
    option (.tlvpickle.OptString) = "d:u:";
    option (.tlvpickle.Usage) = "-d <num>";
  }
}

