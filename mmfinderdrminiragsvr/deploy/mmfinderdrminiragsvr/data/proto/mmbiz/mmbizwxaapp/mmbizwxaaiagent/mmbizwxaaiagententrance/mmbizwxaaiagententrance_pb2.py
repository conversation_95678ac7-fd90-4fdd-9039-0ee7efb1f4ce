# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: mmbiz/mmbizwxaapp/mmbizwxaaiagent/mmbizwxaaiagententrance/mmbizwxaaiagententrance.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from comm2.tlvpickle import skbuiltintype_pb2 as comm2_dot_tlvpickle_dot_skbuiltintype__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\nWmmbiz/mmbizwxaapp/mmbizwxaaiagent/mmbizwxaaiagententrance/mmbizwxaaiagententrance.proto\x12\x17mmbizwxaaiagententrance\x1a#comm2/tlvpickle/skbuiltintype.proto\"H\n\x13StreamRespWxChatReq\x12\x0f\n\x07useruin\x18\x01 \x01(\r\x12\x10\n\x08\x61sr_text\x18\x02 \x01(\t\x12\x0e\n\x06\x62ypass\x18\x03 \x01(\x0c\"{\n\x14StreamRespWxChatResp\x12\x14\n\x0cmodel_result\x18\x01 \x01(\t\x12\x10\n\x08tts_text\x18\x02 \x01(\t\x12\x0e\n\x06\x62ypass\x18\x03 \x01(\t\x12\x0b\n\x03\x63md\x18\x04 \x01(\r\x12\x1e\n\x16notify_client_msg_json\x18\x05 \x01(\t\")\n\x0fNotifyClientMsg\x12\x16\n\x0e\x64isplay_result\x18\x01 \x01(\r\"\x87\x01\n\nWxaAppItem\x12\r\n\x05\x61ppid\x18\x01 \x01(\t\x12\x10\n\x08nickname\x18\x02 \x01(\t\x12\x14\n\x0cversion_type\x18\x03 \x01(\r\x12\x13\n\x0bupdate_time\x18\x04 \x01(\r\x12\x13\n\x0b\x63reate_time\x18\x05 \x01(\r\x12\x18\n\x10\x66riends_used_num\x18\n \x01(\r\"\x8c\x02\n\x14GetWxaUsageRecordReq\x12\x0f\n\x07useruin\x18\x01 \x01(\r\x12\x11\n\tcondition\x18\n \x01(\r\x12\x41\n\x05scene\x18\x1e \x01(\x0e\x32).mmbizwxaaiagententrance.enWhiteListScene:\x07\x44\x45\x46\x41ULT\"\x8c\x01\n\"enMMBizWxaAiAgentEntranceCondition\x12\'\n#GET_HISTORY_BY_UPDATETIME_CONDITION\x10\x01\x12\x1b\n\x17GET_STAR_LIST_CONDITION\x10\x02\x12 \n\x1cGET_COMMON_USE_APP_CONDITION\x10\x04\"\xc5\x01\n\x15GetWxaUsageRecordResp\x12\x39\n\x0chistory_list\x18\x01 \x03(\x0b\x32#.mmbizwxaaiagententrance.WxaAppItem\x12\x36\n\tstar_list\x18\x02 \x03(\x0b\x32#.mmbizwxaaiagententrance.WxaAppItem\x12\x39\n\x0c\x63ommuse_list\x18\x03 \x03(\x0b\x32#.mmbizwxaaiagententrance.WxaAppItem\"\x86\x01\n\x10GetWxaFeatureReq\x12\x0f\n\x07useruin\x18\x01 \x01(\r\x12\x13\n\x0b\x62izuin_list\x18\x02 \x03(\r\x12\x12\n\nappid_list\x18\x03 \x03(\t\x12\x38\n\x05scene\x18\n \x01(\x0e\x32).mmbizwxaaiagententrance.enWhiteListScene\"\xad\x01\n\x11GetWxaFeatureResp\x12O\n\x10wxa_feature_list\x18\x01 \x03(\x0b\x32\x35.mmbizwxaaiagententrance.GetWxaFeatureResp.WxaFeature\x1aG\n\nWxaFeature\x12\r\n\x05\x61ppid\x18\x01 \x01(\t\x12\x10\n\x08nickname\x18\x03 \x01(\t\x12\x18\n\x10\x66riends_used_num\x18\n \x01(\r*<\n\x10\x65nWhiteListScene\x12\x0b\n\x07\x44\x45\x46\x41ULT\x10\x01\x12\x1b\n\x17\x41I_AGENT_ENTRANCE_SCENE\x10\x02\x32\xb2\x03\n\x17MMBizWxaAiAgentEntrance\x12\x8e\x01\n\x10StreamRespWxChat\x12,.mmbizwxaaiagententrance.StreamRespWxChatReq\x1a-.mmbizwxaaiagententrance.StreamRespWxChatResp\"\x1b\x80\xa4\xe8\x03\x01\x8a\xa4\xe8\x03\x04\x64:u:\x92\xa4\xe8\x03\x08-d <num>0\x01\x12\x83\x01\n\x11GetWxaUsageRecord\x12-.mmbizwxaaiagententrance.GetWxaUsageRecordReq\x1a..mmbizwxaaiagententrance.GetWxaUsageRecordResp\"\x0f\x80\xa4\xe8\x03\x02\x8a\xa4\xe8\x03\x00\x92\xa4\xe8\x03\x00\x12w\n\rGetWxaFeature\x12).mmbizwxaaiagententrance.GetWxaFeatureReq\x1a*.mmbizwxaaiagententrance.GetWxaFeatureResp\"\x0f\x80\xa4\xe8\x03\x03\x8a\xa4\xe8\x03\x00\x92\xa4\xe8\x03\x00\x1a\x07\x88\xa4\xe8\x03\x95\xe3\x01')

_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, globals())
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'mmbiz.mmbizwxaapp.mmbizwxaaiagent.mmbizwxaaiagententrance.mmbizwxaaiagententrance_pb2', globals())
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  _MMBIZWXAAIAGENTENTRANCE._options = None
  _MMBIZWXAAIAGENTENTRANCE._serialized_options = b'\210\244\350\003\225\343\001'
  _MMBIZWXAAIAGENTENTRANCE.methods_by_name['StreamRespWxChat']._options = None
  _MMBIZWXAAIAGENTENTRANCE.methods_by_name['StreamRespWxChat']._serialized_options = b'\200\244\350\003\001\212\244\350\003\004d:u:\222\244\350\003\010-d <num>'
  _MMBIZWXAAIAGENTENTRANCE.methods_by_name['GetWxaUsageRecord']._options = None
  _MMBIZWXAAIAGENTENTRANCE.methods_by_name['GetWxaUsageRecord']._serialized_options = b'\200\244\350\003\002\212\244\350\003\000\222\244\350\003\000'
  _MMBIZWXAAIAGENTENTRANCE.methods_by_name['GetWxaFeature']._options = None
  _MMBIZWXAAIAGENTENTRANCE.methods_by_name['GetWxaFeature']._serialized_options = b'\200\244\350\003\003\212\244\350\003\000\222\244\350\003\000'
  _ENWHITELISTSCENE._serialized_start=1317
  _ENWHITELISTSCENE._serialized_end=1377
  _STREAMRESPWXCHATREQ._serialized_start=153
  _STREAMRESPWXCHATREQ._serialized_end=225
  _STREAMRESPWXCHATRESP._serialized_start=227
  _STREAMRESPWXCHATRESP._serialized_end=350
  _NOTIFYCLIENTMSG._serialized_start=352
  _NOTIFYCLIENTMSG._serialized_end=393
  _WXAAPPITEM._serialized_start=396
  _WXAAPPITEM._serialized_end=531
  _GETWXAUSAGERECORDREQ._serialized_start=534
  _GETWXAUSAGERECORDREQ._serialized_end=802
  _GETWXAUSAGERECORDREQ_ENMMBIZWXAAIAGENTENTRANCECONDITION._serialized_start=662
  _GETWXAUSAGERECORDREQ_ENMMBIZWXAAIAGENTENTRANCECONDITION._serialized_end=802
  _GETWXAUSAGERECORDRESP._serialized_start=805
  _GETWXAUSAGERECORDRESP._serialized_end=1002
  _GETWXAFEATUREREQ._serialized_start=1005
  _GETWXAFEATUREREQ._serialized_end=1139
  _GETWXAFEATURERESP._serialized_start=1142
  _GETWXAFEATURERESP._serialized_end=1315
  _GETWXAFEATURERESP_WXAFEATURE._serialized_start=1244
  _GETWXAFEATURERESP_WXAFEATURE._serialized_end=1315
  _MMBIZWXAAIAGENTENTRANCE._serialized_start=1380
  _MMBIZWXAAIAGENTENTRANCE._serialized_end=1814
# @@protoc_insertion_point(module_scope)
