{"1": {"name": "StreamRespWxChat", "input": {"type": "StreamRespWxChatReq", "proto": "mmbiz/mmbizwxaapp/mmbizwxaaiagent/mmbizwxaaiagententrance/mmbizwxaaiagententrance.proto", "cpp_class": "mmbizwxaaiagententrance::StreamRespWxChatReq", "cpp_header": "mmbiz/mmbizwxaapp/mmbizwxaaiagent/mmbizwxaaiagententrance/mmbizwxaaiagententrance.pb.h", "package": "mmbiz.mmbizwxaapp.mmbizwxaaiagent.mmbizwxaaiagententrance.mmbizwxaaiagententrance_pb2"}, "output": {"type": "StreamRespWxChatResp", "proto": "mmbiz/mmbizwxaapp/mmbizwxaaiagent/mmbizwxaaiagententrance/mmbizwxaaiagententrance.proto", "cpp_class": "mmbizwxaaiagententrance::StreamRespWxChatResp", "cpp_header": "mmbiz/mmbizwxaapp/mmbizwxaaiagent/mmbizwxaaiagententrance/mmbizwxaaiagententrance.pb.h", "package": "mmbiz.mmbizwxaapp.mmbizwxaaiagent.mmbizwxaaiagententrance.mmbizwxaaiagententrance_pb2"}}, "2": {"name": "GetWxaUsageRecord", "input": {"type": "GetWxaUsageRecordReq", "proto": "mmbiz/mmbizwxaapp/mmbizwxaaiagent/mmbizwxaaiagententrance/mmbizwxaaiagententrance.proto", "cpp_class": "mmbizwxaaiagententrance::GetWxaUsageRecordReq", "cpp_header": "mmbiz/mmbizwxaapp/mmbizwxaaiagent/mmbizwxaaiagententrance/mmbizwxaaiagententrance.pb.h", "package": "mmbiz.mmbizwxaapp.mmbizwxaaiagent.mmbizwxaaiagententrance.mmbizwxaaiagententrance_pb2"}, "output": {"type": "GetWxaUsageRecordResp", "proto": "mmbiz/mmbizwxaapp/mmbizwxaaiagent/mmbizwxaaiagententrance/mmbizwxaaiagententrance.proto", "cpp_class": "mmbizwxaaiagententrance::GetWxaUsageRecordResp", "cpp_header": "mmbiz/mmbizwxaapp/mmbizwxaaiagent/mmbizwxaaiagententrance/mmbizwxaaiagententrance.pb.h", "package": "mmbiz.mmbizwxaapp.mmbizwxaaiagent.mmbizwxaaiagententrance.mmbizwxaaiagententrance_pb2"}}, "3": {"name": "GetWxaFeature", "input": {"type": "GetWxaFeatureReq", "proto": "mmbiz/mmbizwxaapp/mmbizwxaaiagent/mmbizwxaaiagententrance/mmbizwxaaiagententrance.proto", "cpp_class": "mmbizwxaaiagententrance::GetWxaFeatureReq", "cpp_header": "mmbiz/mmbizwxaapp/mmbizwxaaiagent/mmbizwxaaiagententrance/mmbizwxaaiagententrance.pb.h", "package": "mmbiz.mmbizwxaapp.mmbizwxaaiagent.mmbizwxaaiagententrance.mmbizwxaaiagententrance_pb2"}, "output": {"type": "GetWxaFeatureResp", "proto": "mmbiz/mmbizwxaapp/mmbizwxaaiagent/mmbizwxaaiagententrance/mmbizwxaaiagententrance.proto", "cpp_class": "mmbizwxaaiagententrance::GetWxaFeatureResp", "cpp_header": "mmbiz/mmbizwxaapp/mmbizwxaaiagent/mmbizwxaaiagententrance/mmbizwxaaiagententrance.pb.h", "package": "mmbiz.mmbizwxaapp.mmbizwxaaiagent.mmbizwxaaiagententrance.mmbizwxaaiagententrance_pb2"}}, "20": {"name": "StreamRespTest", "input": {"type": "StreamRespTestReq", "proto": "mmbiz/mmbizwxaapp/mmbizwxaaiagent/mmbizwxaaiagententrance/mmbizwxaaiagententrance.proto", "cpp_class": "mmbizwxaaiagententrance::StreamRespTestReq", "cpp_header": "mmbiz/mmbizwxaapp/mmbizwxaaiagent/mmbizwxaaiagententrance/mmbizwxaaiagententrance.pb.h", "package": "mmbiz.mmbizwxaapp.mmbizwxaaiagent.mmbizwxaaiagententrance.mmbizwxaaiagententrance_pb2"}, "output": {"type": "StreamRespTestResp", "proto": "mmbiz/mmbizwxaapp/mmbizwxaaiagent/mmbizwxaaiagententrance/mmbizwxaaiagententrance.proto", "cpp_class": "mmbizwxaaiagententrance::StreamRespTestResp", "cpp_header": "mmbiz/mmbizwxaapp/mmbizwxaaiagent/mmbizwxaaiagententrance/mmbizwxaaiagententrance.pb.h", "package": "mmbiz.mmbizwxaapp.mmbizwxaaiagent.mmbizwxaaiagententrance.mmbizwxaaiagententrance_pb2"}}, "21": {"name": "GetWhiteList", "input": {"type": "GetWhiteListReq", "proto": "mmbiz/mmbizwxaapp/mmbizwxaaiagent/mmbizwxaaiagententrance/mmbizwxaaiagententrance.proto", "cpp_class": "mmbizwxaaiagententrance::GetWhiteListReq", "cpp_header": "mmbiz/mmbizwxaapp/mmbizwxaaiagent/mmbizwxaaiagententrance/mmbizwxaaiagententrance.pb.h", "package": "mmbiz.mmbizwxaapp.mmbizwxaaiagent.mmbizwxaaiagententrance.mmbizwxaaiagententrance_pb2"}, "output": {"type": "GetWhiteListResp", "proto": "mmbiz/mmbizwxaapp/mmbizwxaaiagent/mmbizwxaaiagententrance/mmbizwxaaiagententrance.proto", "cpp_class": "mmbizwxaaiagententrance::GetWhiteListResp", "cpp_header": "mmbiz/mmbizwxaapp/mmbizwxaaiagent/mmbizwxaaiagententrance/mmbizwxaaiagententrance.pb.h", "package": "mmbiz.mmbizwxaapp.mmbizwxaaiagent.mmbizwxaaiagententrance.mmbizwxaaiagententrance_pb2"}}, "22": {"name": "SetWhiteList", "input": {"type": "SetWhiteListReq", "proto": "mmbiz/mmbizwxaapp/mmbizwxaaiagent/mmbizwxaaiagententrance/mmbizwxaaiagententrance.proto", "cpp_class": "mmbizwxaaiagententrance::SetWhiteListReq", "cpp_header": "mmbiz/mmbizwxaapp/mmbizwxaaiagent/mmbizwxaaiagententrance/mmbizwxaaiagententrance.pb.h", "package": "mmbiz.mmbizwxaapp.mmbizwxaaiagent.mmbizwxaaiagententrance.mmbizwxaaiagententrance_pb2"}, "output": {"type": "SetWhiteListResp", "proto": "mmbiz/mmbizwxaapp/mmbizwxaaiagent/mmbizwxaaiagententrance/mmbizwxaaiagententrance.proto", "cpp_class": "mmbizwxaaiagententrance::SetWhiteListResp", "cpp_header": "mmbiz/mmbizwxaapp/mmbizwxaaiagent/mmbizwxaaiagententrance/mmbizwxaaiagententrance.pb.h", "package": "mmbiz.mmbizwxaapp.mmbizwxaaiagent.mmbizwxaaiagententrance.mmbizwxaaiagententrance_pb2"}}, "23": {"name": "CheckWhiteList", "input": {"type": "CheckWhiteListReq", "proto": "mmbiz/mmbizwxaapp/mmbizwxaaiagent/mmbizwxaaiagententrance/mmbizwxaaiagententrance.proto", "cpp_class": "mmbizwxaaiagententrance::CheckWhiteListReq", "cpp_header": "mmbiz/mmbizwxaapp/mmbizwxaaiagent/mmbizwxaaiagententrance/mmbizwxaaiagententrance.pb.h", "package": "mmbiz.mmbizwxaapp.mmbizwxaaiagent.mmbizwxaaiagententrance.mmbizwxaaiagententrance_pb2"}, "output": {"type": "CheckWhiteListResp", "proto": "mmbiz/mmbizwxaapp/mmbizwxaaiagent/mmbizwxaaiagententrance/mmbizwxaaiagententrance.proto", "cpp_class": "mmbizwxaaiagententrance::CheckWhiteListResp", "cpp_header": "mmbiz/mmbizwxaapp/mmbizwxaaiagent/mmbizwxaaiagententrance/mmbizwxaaiagententrance.pb.h", "package": "mmbiz.mmbizwxaapp.mmbizwxaaiagent.mmbizwxaaiagententrance.mmbizwxaaiagententrance_pb2"}}}