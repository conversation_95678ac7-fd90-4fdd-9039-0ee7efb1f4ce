syntax = "proto2";

import "comm2/tlvpickle/skbuiltintype.proto";
import "mmgateway/proto/mmassistant/openmsg.proto";
package mmbizwxaaiagententrance;

enum enWhiteListScene {
  DEFAULT = 1;
  AI_AGENT_ENTRANCE_SCENE = 2;
}

message StreamRespTestReq {
  optional string input = 1;
}

message StreamRespTestResp {
  optional string output = 1;
}

message StreamRespWxChatReq {
  optional uint32 useruin = 1;
  optional string asr_text = 2;
  optional bytes bypass = 3;
  repeated .wxassistantopenmsg.PostMessageToYBReq history = 4;
}

message StreamRespWxChatResp {
  optional string model_result = 1;
  optional string tts_text = 2;
  optional string bypass = 3;
  optional uint32 cmd = 4;
  optional string notify_client_msg_json = 5;
}

message NotifyClientMsg {
  optional uint32 display_result = 1;
}

message WxaAppItem {
  optional string appid = 1;
  optional string nickname = 2;
  optional uint32 version_type = 3;
  optional uint32 update_time = 4;
  optional uint32 create_time = 5;
  optional uint32 friends_used_num = 10;
  optional uint32 bizuin = 99;
  optional double rank_score = 98;
}

message GetWxaUsageRecordReq {
  enum enMMBizWxaAiAgentEntranceCondition {
    GET_HISTORY_BY_UPDATETIME_CONDITION = 1;
    GET_STAR_LIST_CONDITION = 2;
    GET_COMMON_USE_APP_CONDITION = 4;
  }
  optional uint32 useruin = 1;
  optional uint32 condition = 10;
  optional .mmbizwxaaiagententrance.enWhiteListScene scene = 30 [default = DEFAULT];
}

message GetWxaUsageRecordResp {
  repeated .mmbizwxaaiagententrance.WxaAppItem history_list = 1;
  repeated .mmbizwxaaiagententrance.WxaAppItem star_list = 2;
  repeated .mmbizwxaaiagententrance.WxaAppItem commuse_list = 3;
}

message GetWxaCommUseRecordReq {
  optional uint32 useruin = 1;
}

message GetWxaCommUseRecordResp {
  optional .mmbizwxaaiagententrance.CommUseAppInfo profile = 1;
}

message CommUseAppInfo {
  repeated .mmbizwxaaiagententrance.WxaAppItem common_use = 1;
}

message GetWxaFeatureReq {
  optional uint32 useruin = 1;
  repeated uint32 bizuin_list = 2;
  repeated string appid_list = 3;
  optional .mmbizwxaaiagententrance.enWhiteListScene scene = 10;
}

message GetWxaFeatureResp {
  message WxaFeature {
    optional string appid = 1;
    optional uint32 bizuin = 2;
    optional string nickname = 3;
    optional uint32 friends_used_num = 10;
  }
  repeated .mmbizwxaaiagententrance.GetWxaFeatureResp.WxaFeature wxa_feature_list = 1;
}

message WhiteList {
  optional uint32 scene = 1;
  repeated uint32 useruin_list = 2;
}

message GetWhiteListReq {
  optional .mmbizwxaaiagententrance.enWhiteListScene scene = 1;
}

message GetWhiteListResp {
  repeated uint32 useruin_list = 1;
  optional uint32 total = 10;
}

message SetWhiteListReq {
  enum enOperationType {
    OVERWRITE = 1;
    APPEND = 2;
  }
  optional .mmbizwxaaiagententrance.enWhiteListScene scene = 1;
  repeated uint32 useruin_list = 2;
  optional .mmbizwxaaiagententrance.SetWhiteListReq.enOperationType operation_type = 10 [default = OVERWRITE];
}

message SetWhiteListResp {
  optional uint32 total = 1;
}

message CheckWhiteListReq {
  optional .mmbizwxaaiagententrance.enWhiteListScene scene = 1;
  optional uint32 useruin = 2;
  optional uint32 add_if_missing = 10 [default = 1];
}

message CheckWhiteListResp {
  optional uint32 exist = 1;
  optional uint32 added_if_missing = 10;
  optional uint32 total = 11;
  optional uint32 allowed_total = 12;
}

service MMBizWxaAiAgentEntrance {
  option (.tlvpickle.Magic) = 29077;
  rpc StreamRespWxChat(.mmbizwxaaiagententrance.StreamRespWxChatReq) returns (stream .mmbizwxaaiagententrance.StreamRespWxChatResp) {
    option (.tlvpickle.CmdID) = 1;
    option (.tlvpickle.OptString) = "u:s:";
    option (.tlvpickle.Usage) = "-u <uin> -s <str>";
  }
  rpc GetWxaUsageRecord(.mmbizwxaaiagententrance.GetWxaUsageRecordReq) returns (.mmbizwxaaiagententrance.GetWxaUsageRecordResp) {
    option (.tlvpickle.CmdID) = 2;
    option (.tlvpickle.OptString) = "";
    option (.tlvpickle.Usage) = "";
  }
  rpc GetWxaFeature(.mmbizwxaaiagententrance.GetWxaFeatureReq) returns (.mmbizwxaaiagententrance.GetWxaFeatureResp) {
    option (.tlvpickle.CmdID) = 3;
    option (.tlvpickle.OptString) = "";
    option (.tlvpickle.Usage) = "";
  }
  rpc StreamRespTest(.mmbizwxaaiagententrance.StreamRespTestReq) returns (stream .mmbizwxaaiagententrance.StreamRespTestResp) {
    option (.tlvpickle.CmdID) = 20;
    option (.tlvpickle.OptString) = "d:u:";
    option (.tlvpickle.Usage) = "-d <num>";
  }
  rpc GetWhiteList(.mmbizwxaaiagententrance.GetWhiteListReq) returns (.mmbizwxaaiagententrance.GetWhiteListResp) {
    option (.tlvpickle.CmdID) = 21;
    option (.tlvpickle.OptString) = "";
    option (.tlvpickle.Usage) = "";
    option (.tlvpickle.Brief) = "\350\216\267\345\217\226\347\231\275\345\220\215\345\215\225\357\274\210\346\265\213\350\257\225\346\216\245\345\217\243 \346\226\271\344\276\277\350\260\203\350\257\225 \345\220\216\347\273\255\344\274\232\344\270\213\346\216\211\357\274\211";
  }
  rpc SetWhiteList(.mmbizwxaaiagententrance.SetWhiteListReq) returns (.mmbizwxaaiagententrance.SetWhiteListResp) {
    option (.tlvpickle.CmdID) = 22;
    option (.tlvpickle.OptString) = "";
    option (.tlvpickle.Usage) = "";
    option (.tlvpickle.Brief) = "\350\256\276\347\275\256\347\231\275\345\220\215\345\215\225\357\274\210\346\265\213\350\257\225\346\216\245\345\217\243 \346\226\271\344\276\277\350\260\203\350\257\225 \345\220\216\347\273\255\344\274\232\344\270\213\346\216\211\357\274\211";
  }
  rpc CheckWhiteList(.mmbizwxaaiagententrance.CheckWhiteListReq) returns (.mmbizwxaaiagententrance.CheckWhiteListResp) {
    option (.tlvpickle.CmdID) = 23;
    option (.tlvpickle.OptString) = "";
    option (.tlvpickle.Usage) = "";
    option (.tlvpickle.Brief) = "\346\240\241\351\252\214\347\231\275\345\220\215\345\215\225\357\274\210\346\265\213\350\257\225\346\216\245\345\217\243 \346\226\271\344\276\277\350\260\203\350\257\225 \345\220\216\347\273\255\344\274\232\344\270\213\346\216\211\357\274\211";
  }
}

