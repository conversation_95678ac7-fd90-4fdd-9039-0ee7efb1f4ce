# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: mmbiz/mmbizwxaapp/mmbizwxaaiagent/mmbizwxaaiagententrance/mmbizwxaaiagententrance.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import builder as _builder
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from comm2.tlvpickle import skbuiltintype_pb2 as comm2_dot_tlvpickle_dot_skbuiltintype__pb2
from mmgateway.proto.mmassistant import openmsg_pb2 as mmgateway_dot_proto_dot_mmassistant_dot_openmsg__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\nWmmbiz/mmbizwxaapp/mmbizwxaaiagent/mmbizwxaaiagententrance/mmbizwxaaiagententrance.proto\x12\x17mmbizwxaaiagententrance\x1a#comm2/tlvpickle/skbuiltintype.proto\x1a)mmgateway/proto/mmassistant/openmsg.proto\"\"\n\x11StreamRespTestReq\x12\r\n\x05input\x18\x01 \x01(\t\"$\n\x12StreamRespTestResp\x12\x0e\n\x06output\x18\x01 \x01(\t\"\x81\x01\n\x13StreamRespWxChatReq\x12\x0f\n\x07useruin\x18\x01 \x01(\r\x12\x10\n\x08\x61sr_text\x18\x02 \x01(\t\x12\x0e\n\x06\x62ypass\x18\x03 \x01(\x0c\x12\x37\n\x07history\x18\x04 \x03(\x0b\x32&.wxassistantopenmsg.PostMessageToYBReq\"{\n\x14StreamRespWxChatResp\x12\x14\n\x0cmodel_result\x18\x01 \x01(\t\x12\x10\n\x08tts_text\x18\x02 \x01(\t\x12\x0e\n\x06\x62ypass\x18\x03 \x01(\t\x12\x0b\n\x03\x63md\x18\x04 \x01(\r\x12\x1e\n\x16notify_client_msg_json\x18\x05 \x01(\t\")\n\x0fNotifyClientMsg\x12\x16\n\x0e\x64isplay_result\x18\x01 \x01(\r\"\xab\x01\n\nWxaAppItem\x12\r\n\x05\x61ppid\x18\x01 \x01(\t\x12\x10\n\x08nickname\x18\x02 \x01(\t\x12\x14\n\x0cversion_type\x18\x03 \x01(\r\x12\x13\n\x0bupdate_time\x18\x04 \x01(\r\x12\x13\n\x0b\x63reate_time\x18\x05 \x01(\r\x12\x18\n\x10\x66riends_used_num\x18\n \x01(\r\x12\x0e\n\x06\x62izuin\x18\x63 \x01(\r\x12\x12\n\nrank_score\x18\x62 \x01(\x01\"\x8c\x02\n\x14GetWxaUsageRecordReq\x12\x0f\n\x07useruin\x18\x01 \x01(\r\x12\x11\n\tcondition\x18\n \x01(\r\x12\x41\n\x05scene\x18\x1e \x01(\x0e\x32).mmbizwxaaiagententrance.enWhiteListScene:\x07\x44\x45\x46\x41ULT\"\x8c\x01\n\"enMMBizWxaAiAgentEntranceCondition\x12\'\n#GET_HISTORY_BY_UPDATETIME_CONDITION\x10\x01\x12\x1b\n\x17GET_STAR_LIST_CONDITION\x10\x02\x12 \n\x1cGET_COMMON_USE_APP_CONDITION\x10\x04\"\xc5\x01\n\x15GetWxaUsageRecordResp\x12\x39\n\x0chistory_list\x18\x01 \x03(\x0b\x32#.mmbizwxaaiagententrance.WxaAppItem\x12\x36\n\tstar_list\x18\x02 \x03(\x0b\x32#.mmbizwxaaiagententrance.WxaAppItem\x12\x39\n\x0c\x63ommuse_list\x18\x03 \x03(\x0b\x32#.mmbizwxaaiagententrance.WxaAppItem\")\n\x16GetWxaCommUseRecordReq\x12\x0f\n\x07useruin\x18\x01 \x01(\r\"S\n\x17GetWxaCommUseRecordResp\x12\x38\n\x07profile\x18\x01 \x01(\x0b\x32\'.mmbizwxaaiagententrance.CommUseAppInfo\"I\n\x0e\x43ommUseAppInfo\x12\x37\n\ncommon_use\x18\x01 \x03(\x0b\x32#.mmbizwxaaiagententrance.WxaAppItem\"\x86\x01\n\x10GetWxaFeatureReq\x12\x0f\n\x07useruin\x18\x01 \x01(\r\x12\x13\n\x0b\x62izuin_list\x18\x02 \x03(\r\x12\x12\n\nappid_list\x18\x03 \x03(\t\x12\x38\n\x05scene\x18\n \x01(\x0e\x32).mmbizwxaaiagententrance.enWhiteListScene\"\xbd\x01\n\x11GetWxaFeatureResp\x12O\n\x10wxa_feature_list\x18\x01 \x03(\x0b\x32\x35.mmbizwxaaiagententrance.GetWxaFeatureResp.WxaFeature\x1aW\n\nWxaFeature\x12\r\n\x05\x61ppid\x18\x01 \x01(\t\x12\x0e\n\x06\x62izuin\x18\x02 \x01(\r\x12\x10\n\x08nickname\x18\x03 \x01(\t\x12\x18\n\x10\x66riends_used_num\x18\n \x01(\r\"0\n\tWhiteList\x12\r\n\x05scene\x18\x01 \x01(\r\x12\x14\n\x0cuseruin_list\x18\x02 \x03(\r\"K\n\x0fGetWhiteListReq\x12\x38\n\x05scene\x18\x01 \x01(\x0e\x32).mmbizwxaaiagententrance.enWhiteListScene\"7\n\x10GetWhiteListResp\x12\x14\n\x0cuseruin_list\x18\x01 \x03(\r\x12\r\n\x05total\x18\n \x01(\r\"\xec\x01\n\x0fSetWhiteListReq\x12\x38\n\x05scene\x18\x01 \x01(\x0e\x32).mmbizwxaaiagententrance.enWhiteListScene\x12\x14\n\x0cuseruin_list\x18\x02 \x03(\r\x12[\n\x0eoperation_type\x18\n \x01(\x0e\x32\x38.mmbizwxaaiagententrance.SetWhiteListReq.enOperationType:\tOVERWRITE\",\n\x0f\x65nOperationType\x12\r\n\tOVERWRITE\x10\x01\x12\n\n\x06\x41PPEND\x10\x02\"!\n\x10SetWhiteListResp\x12\r\n\x05total\x18\x01 \x01(\r\"y\n\x11\x43heckWhiteListReq\x12\x38\n\x05scene\x18\x01 \x01(\x0e\x32).mmbizwxaaiagententrance.enWhiteListScene\x12\x0f\n\x07useruin\x18\x02 \x01(\r\x12\x19\n\x0e\x61\x64\x64_if_missing\x18\n \x01(\r:\x01\x31\"c\n\x12\x43heckWhiteListResp\x12\r\n\x05\x65xist\x18\x01 \x01(\r\x12\x18\n\x10\x61\x64\x64\x65\x64_if_missing\x18\n \x01(\r\x12\r\n\x05total\x18\x0b \x01(\r\x12\x15\n\rallowed_total\x18\x0c \x01(\r*<\n\x10\x65nWhiteListScene\x12\x0b\n\x07\x44\x45\x46\x41ULT\x10\x01\x12\x1b\n\x17\x41I_AGENT_ENTRANCE_SCENE\x10\x02\x32\xfa\x08\n\x17MMBizWxaAiAgentEntrance\x12\x97\x01\n\x10StreamRespWxChat\x12,.mmbizwxaaiagententrance.StreamRespWxChatReq\x1a-.mmbizwxaaiagententrance.StreamRespWxChatResp\"$\x80\xa4\xe8\x03\x01\x8a\xa4\xe8\x03\x04u:s:\x92\xa4\xe8\x03\x11-u <uin> -s <str>0\x01\x12\x83\x01\n\x11GetWxaUsageRecord\x12-.mmbizwxaaiagententrance.GetWxaUsageRecordReq\x1a..mmbizwxaaiagententrance.GetWxaUsageRecordResp\"\x0f\x80\xa4\xe8\x03\x02\x8a\xa4\xe8\x03\x00\x92\xa4\xe8\x03\x00\x12w\n\rGetWxaFeature\x12).mmbizwxaaiagententrance.GetWxaFeatureReq\x1a*.mmbizwxaaiagententrance.GetWxaFeatureResp\"\x0f\x80\xa4\xe8\x03\x03\x8a\xa4\xe8\x03\x00\x92\xa4\xe8\x03\x00\x12\x88\x01\n\x0eStreamRespTest\x12*.mmbizwxaaiagententrance.StreamRespTestReq\x1a+.mmbizwxaaiagententrance.StreamRespTestResp\"\x1b\x80\xa4\xe8\x03\x14\x8a\xa4\xe8\x03\x04\x64:u:\x92\xa4\xe8\x03\x08-d <num>0\x01\x12\xb7\x01\n\x0cGetWhiteList\x12(.mmbizwxaaiagententrance.GetWhiteListReq\x1a).mmbizwxaaiagententrance.GetWhiteListResp\"R\x80\xa4\xe8\x03\x15\x8a\xa4\xe8\x03\x00\x92\xa4\xe8\x03\x00\xaa\xa4\xe8\x03>\xe8\x8e\xb7\xe5\x8f\x96\xe7\x99\xbd\xe5\x90\x8d\xe5\x8d\x95\xef\xbc\x88\xe6\xb5\x8b\xe8\xaf\x95\xe6\x8e\xa5\xe5\x8f\xa3 \xe6\x96\xb9\xe4\xbe\xbf\xe8\xb0\x83\xe8\xaf\x95 \xe5\x90\x8e\xe7\xbb\xad\xe4\xbc\x9a\xe4\xb8\x8b\xe6\x8e\x89\xef\xbc\x89\x12\xb7\x01\n\x0cSetWhiteList\x12(.mmbizwxaaiagententrance.SetWhiteListReq\x1a).mmbizwxaaiagententrance.SetWhiteListResp\"R\x80\xa4\xe8\x03\x16\x8a\xa4\xe8\x03\x00\x92\xa4\xe8\x03\x00\xaa\xa4\xe8\x03>\xe8\xae\xbe\xe7\xbd\xae\xe7\x99\xbd\xe5\x90\x8d\xe5\x8d\x95\xef\xbc\x88\xe6\xb5\x8b\xe8\xaf\x95\xe6\x8e\xa5\xe5\x8f\xa3 \xe6\x96\xb9\xe4\xbe\xbf\xe8\xb0\x83\xe8\xaf\x95 \xe5\x90\x8e\xe7\xbb\xad\xe4\xbc\x9a\xe4\xb8\x8b\xe6\x8e\x89\xef\xbc\x89\x12\xbd\x01\n\x0e\x43heckWhiteList\x12*.mmbizwxaaiagententrance.CheckWhiteListReq\x1a+.mmbizwxaaiagententrance.CheckWhiteListResp\"R\x80\xa4\xe8\x03\x17\x8a\xa4\xe8\x03\x00\x92\xa4\xe8\x03\x00\xaa\xa4\xe8\x03>\xe6\xa0\xa1\xe9\xaa\x8c\xe7\x99\xbd\xe5\x90\x8d\xe5\x8d\x95\xef\xbc\x88\xe6\xb5\x8b\xe8\xaf\x95\xe6\x8e\xa5\xe5\x8f\xa3 \xe6\x96\xb9\xe4\xbe\xbf\xe8\xb0\x83\xe8\xaf\x95 \xe5\x90\x8e\xe7\xbb\xad\xe4\xbc\x9a\xe4\xb8\x8b\xe6\x8e\x89\xef\xbc\x89\x1a\x07\x88\xa4\xe8\x03\x95\xe3\x01')

_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, globals())
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'mmbiz.mmbizwxaapp.mmbizwxaaiagent.mmbizwxaaiagententrance.mmbizwxaaiagententrance_pb2', globals())
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  _MMBIZWXAAIAGENTENTRANCE._options = None
  _MMBIZWXAAIAGENTENTRANCE._serialized_options = b'\210\244\350\003\225\343\001'
  _MMBIZWXAAIAGENTENTRANCE.methods_by_name['StreamRespWxChat']._options = None
  _MMBIZWXAAIAGENTENTRANCE.methods_by_name['StreamRespWxChat']._serialized_options = b'\200\244\350\003\001\212\244\350\003\004u:s:\222\244\350\003\021-u <uin> -s <str>'
  _MMBIZWXAAIAGENTENTRANCE.methods_by_name['GetWxaUsageRecord']._options = None
  _MMBIZWXAAIAGENTENTRANCE.methods_by_name['GetWxaUsageRecord']._serialized_options = b'\200\244\350\003\002\212\244\350\003\000\222\244\350\003\000'
  _MMBIZWXAAIAGENTENTRANCE.methods_by_name['GetWxaFeature']._options = None
  _MMBIZWXAAIAGENTENTRANCE.methods_by_name['GetWxaFeature']._serialized_options = b'\200\244\350\003\003\212\244\350\003\000\222\244\350\003\000'
  _MMBIZWXAAIAGENTENTRANCE.methods_by_name['StreamRespTest']._options = None
  _MMBIZWXAAIAGENTENTRANCE.methods_by_name['StreamRespTest']._serialized_options = b'\200\244\350\003\024\212\244\350\003\004d:u:\222\244\350\003\010-d <num>'
  _MMBIZWXAAIAGENTENTRANCE.methods_by_name['GetWhiteList']._options = None
  _MMBIZWXAAIAGENTENTRANCE.methods_by_name['GetWhiteList']._serialized_options = b'\200\244\350\003\025\212\244\350\003\000\222\244\350\003\000\252\244\350\003>\350\216\267\345\217\226\347\231\275\345\220\215\345\215\225\357\274\210\346\265\213\350\257\225\346\216\245\345\217\243 \346\226\271\344\276\277\350\260\203\350\257\225 \345\220\216\347\273\255\344\274\232\344\270\213\346\216\211\357\274\211'
  _MMBIZWXAAIAGENTENTRANCE.methods_by_name['SetWhiteList']._options = None
  _MMBIZWXAAIAGENTENTRANCE.methods_by_name['SetWhiteList']._serialized_options = b'\200\244\350\003\026\212\244\350\003\000\222\244\350\003\000\252\244\350\003>\350\256\276\347\275\256\347\231\275\345\220\215\345\215\225\357\274\210\346\265\213\350\257\225\346\216\245\345\217\243 \346\226\271\344\276\277\350\260\203\350\257\225 \345\220\216\347\273\255\344\274\232\344\270\213\346\216\211\357\274\211'
  _MMBIZWXAAIAGENTENTRANCE.methods_by_name['CheckWhiteList']._options = None
  _MMBIZWXAAIAGENTENTRANCE.methods_by_name['CheckWhiteList']._serialized_options = b'\200\244\350\003\027\212\244\350\003\000\222\244\350\003\000\252\244\350\003>\346\240\241\351\252\214\347\231\275\345\220\215\345\215\225\357\274\210\346\265\213\350\257\225\346\216\245\345\217\243 \346\226\271\344\276\277\350\260\203\350\257\225 \345\220\216\347\273\255\344\274\232\344\270\213\346\216\211\357\274\211'
  _ENWHITELISTSCENE._serialized_start=2429
  _ENWHITELISTSCENE._serialized_end=2489
  _STREAMRESPTESTREQ._serialized_start=196
  _STREAMRESPTESTREQ._serialized_end=230
  _STREAMRESPTESTRESP._serialized_start=232
  _STREAMRESPTESTRESP._serialized_end=268
  _STREAMRESPWXCHATREQ._serialized_start=271
  _STREAMRESPWXCHATREQ._serialized_end=400
  _STREAMRESPWXCHATRESP._serialized_start=402
  _STREAMRESPWXCHATRESP._serialized_end=525
  _NOTIFYCLIENTMSG._serialized_start=527
  _NOTIFYCLIENTMSG._serialized_end=568
  _WXAAPPITEM._serialized_start=571
  _WXAAPPITEM._serialized_end=742
  _GETWXAUSAGERECORDREQ._serialized_start=745
  _GETWXAUSAGERECORDREQ._serialized_end=1013
  _GETWXAUSAGERECORDREQ_ENMMBIZWXAAIAGENTENTRANCECONDITION._serialized_start=873
  _GETWXAUSAGERECORDREQ_ENMMBIZWXAAIAGENTENTRANCECONDITION._serialized_end=1013
  _GETWXAUSAGERECORDRESP._serialized_start=1016
  _GETWXAUSAGERECORDRESP._serialized_end=1213
  _GETWXACOMMUSERECORDREQ._serialized_start=1215
  _GETWXACOMMUSERECORDREQ._serialized_end=1256
  _GETWXACOMMUSERECORDRESP._serialized_start=1258
  _GETWXACOMMUSERECORDRESP._serialized_end=1341
  _COMMUSEAPPINFO._serialized_start=1343
  _COMMUSEAPPINFO._serialized_end=1416
  _GETWXAFEATUREREQ._serialized_start=1419
  _GETWXAFEATUREREQ._serialized_end=1553
  _GETWXAFEATURERESP._serialized_start=1556
  _GETWXAFEATURERESP._serialized_end=1745
  _GETWXAFEATURERESP_WXAFEATURE._serialized_start=1658
  _GETWXAFEATURERESP_WXAFEATURE._serialized_end=1745
  _WHITELIST._serialized_start=1747
  _WHITELIST._serialized_end=1795
  _GETWHITELISTREQ._serialized_start=1797
  _GETWHITELISTREQ._serialized_end=1872
  _GETWHITELISTRESP._serialized_start=1874
  _GETWHITELISTRESP._serialized_end=1929
  _SETWHITELISTREQ._serialized_start=1932
  _SETWHITELISTREQ._serialized_end=2168
  _SETWHITELISTREQ_ENOPERATIONTYPE._serialized_start=2124
  _SETWHITELISTREQ_ENOPERATIONTYPE._serialized_end=2168
  _SETWHITELISTRESP._serialized_start=2170
  _SETWHITELISTRESP._serialized_end=2203
  _CHECKWHITELISTREQ._serialized_start=2205
  _CHECKWHITELISTREQ._serialized_end=2326
  _CHECKWHITELISTRESP._serialized_start=2328
  _CHECKWHITELISTRESP._serialized_end=2427
  _MMBIZWXAAIAGENTENTRANCE._serialized_start=2492
  _MMBIZWXAAIAGENTENTRANCE._serialized_end=3638
# @@protoc_insertion_point(module_scope)
