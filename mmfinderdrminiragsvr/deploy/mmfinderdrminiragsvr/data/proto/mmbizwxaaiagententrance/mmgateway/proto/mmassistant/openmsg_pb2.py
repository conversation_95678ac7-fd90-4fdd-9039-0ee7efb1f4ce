# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: mmgateway/proto/mmassistant/openmsg.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import builder as _builder
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n)mmgateway/proto/mmassistant/openmsg.proto\x12\x12wxassistantopenmsg\"(\n\x08\x43\x44NMedia\x12\x0b\n\x03url\x18\x01 \x01(\t\x12\x0f\n\x07\x61\x65s_key\x18\x02 \x01(\x0c\"d\n\x04\x46ile\x12\x11\n\tfile_name\x18\x01 \x01(\t\x12\x0b\n\x03md5\x18\x02 \x01(\t\x12\x0b\n\x03len\x18\x03 \x01(\t\x12/\n\tcdn_media\x18\x04 \x01(\x0b\x32\x1c.wxassistantopenmsg.CDNMedia\"\xb7\x01\n\x05Voice\x12+\n\x05media\x18\x01 \x01(\x0b\x32\x1c.wxassistantopenmsg.CDNMedia\x12\x13\n\x0b\x65ncode_type\x18\x02 \x01(\r\x12\x17\n\x0f\x62its_per_sample\x18\x03 \x01(\r\x12\x13\n\x0bsample_rate\x18\x04 \x01(\r\x12\x10\n\x08playtime\x18\x05 \x01(\r\x12\x12\n\nvoice_text\x18\x0b \x01(\t\x12\x12\n\nvoice_data\x18\x0c \x01(\tJ\x04\x08\x06\x10\x0b\"5\n\x06\x41ppUrl\x12\x0b\n\x03url\x18\x01 \x01(\t\x12\x0f\n\x07\x61ppname\x18\x02 \x01(\t\x12\r\n\x05title\x18\x03 \x01(\t\"\x1b\n\x07NoteMsg\x12\x10\n\x08\x64\x61ta_xml\x18\x01 \x01(\t\"W\n\rFinderFeedMsg\x12\x11\n\tobject_id\x18\x01 \x01(\t\x12\x11\n\tfeed_type\x18\x02 \x01(\r\x12\x11\n\texport_id\x18\x03 \x01(\t\x12\r\n\x05token\x18\x04 \x01(\t\"\xc4\n\n\x0e\x43hatRecordItem\x12\r\n\x05msgid\x18\x01 \x01(\x04\x12\x13\n\x0b\x63reate_time\x18\x02 \x01(\r\x12\x13\n\x0bsender_name\x18\x03 \x01(\t\x12=\n\x04text\x18\x0b \x01(\x0b\x32-.wxassistantopenmsg.ChatRecordItem.RecordTextH\x00\x12?\n\x05image\x18\x0c \x01(\x0b\x32..wxassistantopenmsg.ChatRecordItem.RecordImageH\x00\x12\x30\n\x06record\x18\r \x01(\x0b\x32\x1e.wxassistantopenmsg.ChatRecordH\x00\x12=\n\x04\x66\x65\x65\x64\x18\x0e \x01(\x0b\x32-.wxassistantopenmsg.ChatRecordItem.RecordFeedH\x00\x12\x41\n\x06\x61ppurl\x18\x0f \x01(\x0b\x32/.wxassistantopenmsg.ChatRecordItem.RecordAppUrlH\x00\x12\x43\n\x07product\x18\x10 \x01(\x0b\x32\x30.wxassistantopenmsg.ChatRecordItem.RecordProductH\x00\x12?\n\x05music\x18\x11 \x01(\x0b\x32..wxassistantopenmsg.ChatRecordItem.RecordMusicH\x00\x12?\n\x05video\x18\x12 \x01(\x0b\x32..wxassistantopenmsg.ChatRecordItem.RecordVideoH\x00\x12?\n\x05\x65moji\x18\x13 \x01(\x0b\x32..wxassistantopenmsg.ChatRecordItem.RecordEmojiH\x00\x1a\x46\n\rRecordRefItem\x12\r\n\x05svrid\x18\x01 \x01(\t\x12\x13\n\x0b\x64isplayname\x18\x02 \x01(\t\x12\x11\n\treferdesc\x18\x03 \x01(\t\x1aY\n\nRecordText\x12\x0c\n\x04text\x18\x01 \x01(\t\x12=\n\x03ref\x18\x02 \x01(\x0b\x32\x30.wxassistantopenmsg.ChatRecordItem.RecordRefItem\x1a:\n\x0bRecordImage\x12+\n\x05media\x18\x01 \x01(\x0b\x32\x1c.wxassistantopenmsg.CDNMedia\x1a\x32\n\nRecordFeed\x12\x11\n\tobject_id\x18\x01 \x01(\t\x12\x11\n\tfeed_type\x18\x02 \x01(\r\x1a;\n\x0cRecordAppUrl\x12\x0b\n\x03url\x18\x01 \x01(\t\x12\x0f\n\x07\x61ppname\x18\x02 \x01(\t\x12\r\n\x05title\x18\x03 \x01(\t\x1a\x8b\x01\n\rRecordProduct\x12\x0e\n\x06\x61pp_id\x18\x01 \x01(\t\x12\x12\n\nproduct_id\x18\x02 \x01(\t\x12\x11\n\tcover_url\x18\x03 \x01(\t\x12\x15\n\rproduct_title\x18\x04 \x01(\t\x12\x15\n\rselling_price\x18\x05 \x01(\t\x12\x15\n\rplatform_name\x18\x06 \x01(\t\x1a\x36\n\x0bRecordMusic\x12\x11\n\tlisten_id\x18\x01 \x01(\t\x12\x14\n\x0cget_link_mid\x18\x02 \x01(\t\x1a:\n\x0bRecordVideo\x12+\n\x05media\x18\x01 \x01(\x0b\x32\x1c.wxassistantopenmsg.CDNMedia\x1aZ\n\x0bRecordEmoji\x12\r\n\x05width\x18\x01 \x01(\r\x12\x0e\n\x06height\x18\x02 \x01(\r\x12\x11\n\tproductid\x18\x03 \x01(\t\x12\x0b\n\x03md5\x18\x04 \x01(\t\x12\x0c\n\x04type\x18\x05 \x01(\rB\t\n\x07\x43ontentJ\x04\x08\x04\x10\x0b\">\n\nChatRecord\x12\x30\n\x04item\x18\x01 \x03(\x0b\x32\".wxassistantopenmsg.ChatRecordItem\"\x85\x01\n\x07Product\x12\x0e\n\x06\x61pp_id\x18\x01 \x01(\t\x12\x12\n\nproduct_id\x18\x02 \x01(\t\x12\x11\n\tcover_url\x18\x03 \x01(\t\x12\x15\n\rproduct_title\x18\x04 \x01(\t\x12\x15\n\rselling_price\x18\x05 \x01(\t\x12\x15\n\rplatform_name\x18\x06 \x01(\t\"5\n\nListenFeed\x12\x11\n\tlisten_id\x18\x01 \x01(\t\x12\x14\n\x0cget_link_mid\x18\x02 \x01(\t\"!\n\nListenList\x12\x13\n\x0b\x63\x61tegory_id\x18\x01 \x01(\t\"4\n\x05Video\x12+\n\x05media\x18\x01 \x01(\x0b\x32\x1c.wxassistantopenmsg.CDNMedia\"\x8f\x01\n\x05\x45moji\x12\x0b\n\x03md5\x18\x01 \x01(\t\x12\x0c\n\x04type\x18\x02 \x01(\r\x12\x0e\n\x06length\x18\x03 \x01(\r\x12\r\n\x05thumb\x18\x04 \x01(\t\x12\r\n\x05width\x18\x05 \x01(\r\x12\x0e\n\x06height\x18\x06 \x01(\r\x12\x0c\n\x04\x64\x65sc\x18\x07 \x01(\t\x12\x0b\n\x03url\x18\x08 \x01(\t\x12\x12\n\nproduct_id\x18\t \x01(\t\"6\n\x06PatMsg\x12\x0c\n\x04\x66rom\x18\x01 \x01(\t\x12\x0e\n\x06patted\x18\x02 \x01(\t\x12\x0e\n\x06suffix\x18\x03 \x01(\t\"\x1a\n\nUnKnownMsg\x12\x0c\n\x04type\x18\x01 \x01(\r\"\xb0\x01\n\x08UserInfo\x12\x0e\n\x06openid\x18\x01 \x01(\t\x12\x10\n\x08nickname\x18\x02 \x01(\t\x12\x17\n\x0f\x63hatroom_remark\x18\x03 \x01(\t\x12\x11\n\tuser_type\x18\x04 \x01(\r\x12\x0e\n\x06userip\x18\x05 \x01(\t\x12\x10\n\x08username\x18\x06 \x01(\t\"4\n\x08UserType\x12\x13\n\x0fkUserTypeNormal\x10\x00\x12\x13\n\x0fkUserTypeIntell\x10\x01\"\xfa\x06\n\x0eMessageContent\x12\x0e\n\x04text\x18\x01 \x01(\tH\x00\x12-\n\x05image\x18\x02 \x01(\x0b\x32\x1c.wxassistantopenmsg.CDNMediaH\x00\x12(\n\x04\x66ile\x18\x03 \x01(\x0b\x32\x18.wxassistantopenmsg.FileH\x00\x12*\n\x05voice\x18\x04 \x01(\x0b\x32\x19.wxassistantopenmsg.VoiceH\x00\x12,\n\x06\x61ppurl\x18\x05 \x01(\x0b\x32\x1a.wxassistantopenmsg.AppUrlH\x00\x12/\n\x08note_msg\x18\x06 \x01(\x0b\x32\x1b.wxassistantopenmsg.NoteMsgH\x00\x12\x30\n\x06record\x18\x07 \x01(\x0b\x32\x1e.wxassistantopenmsg.ChatRecordH\x00\x12\x33\n\x06\x66inder\x18\x08 \x01(\x0b\x32!.wxassistantopenmsg.FinderFeedMsgH\x00\x12.\n\x07product\x18\t \x01(\x0b\x32\x1b.wxassistantopenmsg.ProductH\x00\x12\x35\n\x0blisten_feed\x18\n \x01(\x0b\x32\x1e.wxassistantopenmsg.ListenFeedH\x00\x12\x35\n\x0blisten_list\x18\x0b \x01(\x0b\x32\x1e.wxassistantopenmsg.ListenListH\x00\x12*\n\x05video\x18\x0c \x01(\x0b\x32\x19.wxassistantopenmsg.VideoH\x00\x12*\n\x05\x65moji\x18\r \x01(\x0b\x32\x19.wxassistantopenmsg.EmojiH\x00\x12)\n\x03pat\x18\x0e \x01(\x0b\x32\x1a.wxassistantopenmsg.PatMsgH\x00\x12\x31\n\x07unknown\x18\x14 \x01(\x0b\x32\x1e.wxassistantopenmsg.UnKnownMsgH\x00\x12\x36\n\x11reference_message\x18\x15 \x01(\x0b\x32\x1b.wxassistantopenmsg.Message\x12\x32\n\x0c\x61t_user_list\x18\x16 \x03(\x0b\x32\x1c.wxassistantopenmsg.UserInfo\x12\x10\n\x08msg_pack\x18\x17 \x01(\x0c\x12\x17\n\x0fref_msg_content\x18\x18 \x01(\x0c\x12\x11\n\x08\x65xt_info\x18\xe8\x07 \x01(\tB\t\n\x07\x63ontentJ\x04\x08\x0f\x10\x14\"Q\n\x0cMsgOrderInfo\x12\r\n\x05group\x18\x01 \x01(\t\x12\x0f\n\x07list_id\x18\x02 \x01(\x04\x12\x10\n\x08list_len\x18\x03 \x01(\x04\x12\x0f\n\x07item_id\x18\x04 \x01(\x04\"\xa8\x02\n\x0bMessageMeta\x12\r\n\x05msgid\x18\x01 \x01(\x04\x12\x0c\n\x04\x66rom\x18\x02 \x01(\t\x12\x0c\n\x04time\x18\x03 \x01(\x04\x12\r\n\x05\x61ppid\x18\x04 \x01(\t\x12\x13\n\x0btrace_msgid\x18\x05 \x01(\t\x12\x11\n\tchat_type\x18\x06 \x01(\r\x12\x34\n\x0e\x66rom_user_info\x18\x07 \x01(\x0b\x32\x1c.wxassistantopenmsg.UserInfo\x12\x13\n\x0b\x63hatroom_id\x18\x08 \x01(\x04\x12\x34\n\norder_info\x18\t \x01(\x0b\x32 .wxassistantopenmsg.MsgOrderInfo\"6\n\x08\x43hatType\x12\x13\n\x0fkChatTypeSingle\x10\x00\x12\x15\n\x11kChatTypeChatroom\x10\x01\"m\n\x07Message\x12-\n\x04meta\x18\x01 \x01(\x0b\x32\x1f.wxassistantopenmsg.MessageMeta\x12\x33\n\x07\x63ontent\x18\x02 \x01(\x0b\x32\".wxassistantopenmsg.MessageContent\"\x92\x02\n\tYBCommand\x12\x0b\n\x03\x63md\x18\x01 \x01(\r\x12\x10\n\x08sendtime\x18\x02 \x01(\r\x12\x13\n\x0btrace_msgid\x18\x03 \x01(\t\x12;\n\nlogin_info\x18\x04 \x01(\x0b\x32\'.wxassistantopenmsg.YBCommand.LoginInfo\x12\x37\n\x08pos_info\x18\x05 \x01(\x0b\x32%.wxassistantopenmsg.YBCommand.PosInfo\x1a+\n\tLoginInfo\x12\r\n\x05token\x18\x01 \x01(\t\x12\x0f\n\x07task_id\x18\x02 \x01(\t\x1a.\n\x07PosInfo\x12\x10\n\x08latitude\x18\x01 \x01(\x01\x12\x11\n\tlongitude\x18\x02 \x01(\x01\" \n\tTraceInfo\x12\x13\n\x0bsendtime_ms\x18\x01 \x01(\x04\"\xfc\x01\n\x12PostMessageToYBReq\x12\r\n\x05scene\x18\x01 \x01(\r\x12(\n\x03msg\x18\x02 \x01(\x0b\x32\x1b.wxassistantopenmsg.Message\x12*\n\x03\x63md\x18\x03 \x01(\x0b\x32\x1d.wxassistantopenmsg.YBCommand\x12\x0e\n\x06openid\x18\x04 \x01(\t\x12\r\n\x05\x61ppid\x18\x05 \x01(\t\x12\r\n\x05token\x18\x06 \x01(\t\x12\x0c\n\x04\x66lag\x18\x07 \x01(\r\x12\x12\n\nbiz_openid\x18\x08 \x01(\t\x12\x31\n\ntrace_info\x18\x64 \x01(\x0b\x32\x1d.wxassistantopenmsg.TraceInfo\"\xcd\x01\n\x16PostMessageToIntellReq\x12\r\n\x05scene\x18\x01 \x01(\r\x12(\n\x03msg\x18\x02 \x01(\x0b\x32\x1b.wxassistantopenmsg.Message\x12*\n\x03\x63md\x18\x03 \x01(\x0b\x32\x1d.wxassistantopenmsg.YBCommand\x12\x0e\n\x06openid\x18\x04 \x01(\t\x12\r\n\x05\x61ppid\x18\x05 \x01(\t\x12\r\n\x05token\x18\x06 \x01(\t\x12\x0c\n\x04\x66lag\x18\x07 \x01(\r\x12\x12\n\nbiz_openid\x18\x08 \x01(\t\"B\n\rWxMsgRulePack\x12\x0e\n\x06ruleid\x18\x01 \x01(\t\x12\x0f\n\x07msgpack\x18\x02 \x01(\x0c\x12\x10\n\x08sendtime\x18\x03 \x01(\x04*!\n\rUnSupportType\x12\x10\n\x0cUNKNOWN_TYPE\x10\x01*/\n\rPostToYBScene\x12\x0e\n\nE_POST_MSG\x10\x00\x12\x0e\n\nE_POST_CMD\x10\x01*\xba\x01\n\tPostYBCmd\x12\x11\n\rE_CMD_UNKNOWN\x10\x00\x12\x13\n\x0f\x45_CMD_CLEAR_MSG\x10\x01\x12\x16\n\x12\x45_CMD_ENTER_FROMHB\x10\x02\x12\x18\n\x14\x45_CMD_ENTER_FROMCARD\x10\x03\x12\x1a\n\x16\x45_CMD_ENTER_FROMSEARCH\x10\x04\x12\x1a\n\x16\x45_CMD_SEND_LOGIN_TOKEN\x10\x05\x12\x1b\n\x17\x45_CMD_ENTER_FROM_QRSCAN\x10\x06*5\n\tYBMsgFlag\x12\x13\n\x0f\x45_PRESSURE_FLAG\x10\x01\x12\x13\n\x0f\x45_TEST_ENV_FLAG\x10\x02')

_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, globals())
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'mmgateway.proto.mmassistant.openmsg_pb2', globals())
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  _UNSUPPORTTYPE._serialized_start=4900
  _UNSUPPORTTYPE._serialized_end=4933
  _POSTTOYBSCENE._serialized_start=4935
  _POSTTOYBSCENE._serialized_end=4982
  _POSTYBCMD._serialized_start=4985
  _POSTYBCMD._serialized_end=5171
  _YBMSGFLAG._serialized_start=5173
  _YBMSGFLAG._serialized_end=5226
  _CDNMEDIA._serialized_start=65
  _CDNMEDIA._serialized_end=105
  _FILE._serialized_start=107
  _FILE._serialized_end=207
  _VOICE._serialized_start=210
  _VOICE._serialized_end=393
  _APPURL._serialized_start=395
  _APPURL._serialized_end=448
  _NOTEMSG._serialized_start=450
  _NOTEMSG._serialized_end=477
  _FINDERFEEDMSG._serialized_start=479
  _FINDERFEEDMSG._serialized_end=566
  _CHATRECORDITEM._serialized_start=569
  _CHATRECORDITEM._serialized_end=1917
  _CHATRECORDITEM_RECORDREFITEM._serialized_start=1216
  _CHATRECORDITEM_RECORDREFITEM._serialized_end=1286
  _CHATRECORDITEM_RECORDTEXT._serialized_start=1288
  _CHATRECORDITEM_RECORDTEXT._serialized_end=1377
  _CHATRECORDITEM_RECORDIMAGE._serialized_start=1379
  _CHATRECORDITEM_RECORDIMAGE._serialized_end=1437
  _CHATRECORDITEM_RECORDFEED._serialized_start=1439
  _CHATRECORDITEM_RECORDFEED._serialized_end=1489
  _CHATRECORDITEM_RECORDAPPURL._serialized_start=1491
  _CHATRECORDITEM_RECORDAPPURL._serialized_end=1550
  _CHATRECORDITEM_RECORDPRODUCT._serialized_start=1553
  _CHATRECORDITEM_RECORDPRODUCT._serialized_end=1692
  _CHATRECORDITEM_RECORDMUSIC._serialized_start=1694
  _CHATRECORDITEM_RECORDMUSIC._serialized_end=1748
  _CHATRECORDITEM_RECORDVIDEO._serialized_start=1750
  _CHATRECORDITEM_RECORDVIDEO._serialized_end=1808
  _CHATRECORDITEM_RECORDEMOJI._serialized_start=1810
  _CHATRECORDITEM_RECORDEMOJI._serialized_end=1900
  _CHATRECORD._serialized_start=1919
  _CHATRECORD._serialized_end=1981
  _PRODUCT._serialized_start=1984
  _PRODUCT._serialized_end=2117
  _LISTENFEED._serialized_start=2119
  _LISTENFEED._serialized_end=2172
  _LISTENLIST._serialized_start=2174
  _LISTENLIST._serialized_end=2207
  _VIDEO._serialized_start=2209
  _VIDEO._serialized_end=2261
  _EMOJI._serialized_start=2264
  _EMOJI._serialized_end=2407
  _PATMSG._serialized_start=2409
  _PATMSG._serialized_end=2463
  _UNKNOWNMSG._serialized_start=2465
  _UNKNOWNMSG._serialized_end=2491
  _USERINFO._serialized_start=2494
  _USERINFO._serialized_end=2670
  _USERINFO_USERTYPE._serialized_start=2618
  _USERINFO_USERTYPE._serialized_end=2670
  _MESSAGECONTENT._serialized_start=2673
  _MESSAGECONTENT._serialized_end=3563
  _MSGORDERINFO._serialized_start=3565
  _MSGORDERINFO._serialized_end=3646
  _MESSAGEMETA._serialized_start=3649
  _MESSAGEMETA._serialized_end=3945
  _MESSAGEMETA_CHATTYPE._serialized_start=3891
  _MESSAGEMETA_CHATTYPE._serialized_end=3945
  _MESSAGE._serialized_start=3947
  _MESSAGE._serialized_end=4056
  _YBCOMMAND._serialized_start=4059
  _YBCOMMAND._serialized_end=4333
  _YBCOMMAND_LOGININFO._serialized_start=4242
  _YBCOMMAND_LOGININFO._serialized_end=4285
  _YBCOMMAND_POSINFO._serialized_start=4287
  _YBCOMMAND_POSINFO._serialized_end=4333
  _TRACEINFO._serialized_start=4335
  _TRACEINFO._serialized_end=4367
  _POSTMESSAGETOYBREQ._serialized_start=4370
  _POSTMESSAGETOYBREQ._serialized_end=4622
  _POSTMESSAGETOINTELLREQ._serialized_start=4625
  _POSTMESSAGETOINTELLREQ._serialized_end=4830
  _WXMSGRULEPACK._serialized_start=4832
  _WXMSGRULEPACK._serialized_end=4898
# @@protoc_insertion_point(module_scope)
