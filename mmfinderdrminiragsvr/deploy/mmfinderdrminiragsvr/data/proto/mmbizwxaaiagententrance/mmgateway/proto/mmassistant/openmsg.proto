syntax = "proto2";

package wxassistantopenmsg;

enum UnSupportType {
  UNKNOWN_TYPE = 1;
}

enum PostToYBScene {
  E_POST_MSG = 0;
  E_POST_CMD = 1;
}

enum PostYBCmd {
  E_CMD_UNKNOWN = 0;
  E_CMD_CLEAR_MSG = 1;
  E_CMD_ENTER_FROMHB = 2;
  E_CMD_ENTER_FROMCARD = 3;
  E_CMD_ENTER_FROMSEARCH = 4;
  E_CMD_SEND_LOGIN_TOKEN = 5;
  E_CMD_ENTER_FROM_QRSCAN = 6;
}

enum YBMsgFlag {
  E_PRESSURE_FLAG = 1;
  E_TEST_ENV_FLAG = 2;
}

message CDNMedia {
  optional string url = 1;
  optional bytes aes_key = 2;
}

message File {
  optional string file_name = 1;
  optional string md5 = 2;
  optional string len = 3;
  optional .wxassistantopenmsg.CDNMedia cdn_media = 4;
}

message Voice {
  optional .wxassistantopenmsg.CDNMedia media = 1;
  optional uint32 encode_type = 2;
  optional uint32 bits_per_sample = 3;
  optional uint32 sample_rate = 4;
  optional uint32 playtime = 5;
  optional string voice_text = 11;
  optional string voice_data = 12;
  reserved 6 to 10;
}

message AppUrl {
  optional string url = 1;
  optional string appname = 2;
  optional string title = 3;
}

message NoteMsg {
  optional string data_xml = 1;
}

message FinderFeedMsg {
  optional string object_id = 1;
  optional uint32 feed_type = 2;
  optional string export_id = 3;
  optional string token = 4;
}

message ChatRecordItem {
  message RecordRefItem {
    optional string svrid = 1;
    optional string displayname = 2;
    optional string referdesc = 3;
  }
  message RecordText {
    optional string text = 1;
    optional .wxassistantopenmsg.ChatRecordItem.RecordRefItem ref = 2;
  }
  message RecordImage {
    optional .wxassistantopenmsg.CDNMedia media = 1;
  }
  message RecordFeed {
    optional string object_id = 1;
    optional uint32 feed_type = 2;
  }
  message RecordAppUrl {
    optional string url = 1;
    optional string appname = 2;
    optional string title = 3;
  }
  message RecordProduct {
    optional string app_id = 1;
    optional string product_id = 2;
    optional string cover_url = 3;
    optional string product_title = 4;
    optional string selling_price = 5;
    optional string platform_name = 6;
  }
  message RecordMusic {
    optional string listen_id = 1;
    optional string get_link_mid = 2;
  }
  message RecordVideo {
    optional .wxassistantopenmsg.CDNMedia media = 1;
  }
  message RecordEmoji {
    optional uint32 width = 1;
    optional uint32 height = 2;
    optional string productid = 3;
    optional string md5 = 4;
    optional uint32 type = 5;
  }
  optional uint64 msgid = 1;
  optional uint32 create_time = 2;
  optional string sender_name = 3;
  oneof Content {
    .wxassistantopenmsg.ChatRecordItem.RecordText text = 11;
    .wxassistantopenmsg.ChatRecordItem.RecordImage image = 12;
    .wxassistantopenmsg.ChatRecord record = 13;
    .wxassistantopenmsg.ChatRecordItem.RecordFeed feed = 14;
    .wxassistantopenmsg.ChatRecordItem.RecordAppUrl appurl = 15;
    .wxassistantopenmsg.ChatRecordItem.RecordProduct product = 16;
    .wxassistantopenmsg.ChatRecordItem.RecordMusic music = 17;
    .wxassistantopenmsg.ChatRecordItem.RecordVideo video = 18;
    .wxassistantopenmsg.ChatRecordItem.RecordEmoji emoji = 19;
  }
  reserved 4 to 10;
}

message ChatRecord {
  repeated .wxassistantopenmsg.ChatRecordItem item = 1;
}

message Product {
  optional string app_id = 1;
  optional string product_id = 2;
  optional string cover_url = 3;
  optional string product_title = 4;
  optional string selling_price = 5;
  optional string platform_name = 6;
}

message ListenFeed {
  optional string listen_id = 1;
  optional string get_link_mid = 2;
}

message ListenList {
  optional string category_id = 1;
}

message Video {
  optional .wxassistantopenmsg.CDNMedia media = 1;
}

message Emoji {
  optional string md5 = 1;
  optional uint32 type = 2;
  optional uint32 length = 3;
  optional string thumb = 4;
  optional uint32 width = 5;
  optional uint32 height = 6;
  optional string desc = 7;
  optional string url = 8;
  optional string product_id = 9;
}

message PatMsg {
  optional string from = 1;
  optional string patted = 2;
  optional string suffix = 3;
}

message UnKnownMsg {
  optional uint32 type = 1;
}

message UserInfo {
  enum UserType {
    kUserTypeNormal = 0;
    kUserTypeIntell = 1;
  }
  optional string openid = 1;
  optional string nickname = 2;
  optional string chatroom_remark = 3;
  optional uint32 user_type = 4;
  optional string userip = 5;
  optional string username = 6;
}

message MessageContent {
  oneof content {
    string text = 1;
    .wxassistantopenmsg.CDNMedia image = 2;
    .wxassistantopenmsg.File file = 3;
    .wxassistantopenmsg.Voice voice = 4;
    .wxassistantopenmsg.AppUrl appurl = 5;
    .wxassistantopenmsg.NoteMsg note_msg = 6;
    .wxassistantopenmsg.ChatRecord record = 7;
    .wxassistantopenmsg.FinderFeedMsg finder = 8;
    .wxassistantopenmsg.Product product = 9;
    .wxassistantopenmsg.ListenFeed listen_feed = 10;
    .wxassistantopenmsg.ListenList listen_list = 11;
    .wxassistantopenmsg.Video video = 12;
    .wxassistantopenmsg.Emoji emoji = 13;
    .wxassistantopenmsg.PatMsg pat = 14;
    .wxassistantopenmsg.UnKnownMsg unknown = 20;
  }
  optional .wxassistantopenmsg.Message reference_message = 21;
  repeated .wxassistantopenmsg.UserInfo at_user_list = 22;
  optional bytes msg_pack = 23;
  optional bytes ref_msg_content = 24;
  optional string ext_info = 1000;
  reserved 15 to 19;
}

message MsgOrderInfo {
  optional string group = 1;
  optional uint64 list_id = 2;
  optional uint64 list_len = 3;
  optional uint64 item_id = 4;
}

message MessageMeta {
  enum ChatType {
    kChatTypeSingle = 0;
    kChatTypeChatroom = 1;
  }
  optional uint64 msgid = 1;
  optional string from = 2;
  optional uint64 time = 3;
  optional string appid = 4;
  optional string trace_msgid = 5;
  optional uint32 chat_type = 6;
  optional .wxassistantopenmsg.UserInfo from_user_info = 7;
  optional uint64 chatroom_id = 8;
  optional .wxassistantopenmsg.MsgOrderInfo order_info = 9;
}

message Message {
  optional .wxassistantopenmsg.MessageMeta meta = 1;
  optional .wxassistantopenmsg.MessageContent content = 2;
}

message YBCommand {
  message LoginInfo {
    optional string token = 1;
    optional string task_id = 2;
  }
  message PosInfo {
    optional double latitude = 1;
    optional double longitude = 2;
  }
  optional uint32 cmd = 1;
  optional uint32 sendtime = 2;
  optional string trace_msgid = 3;
  optional .wxassistantopenmsg.YBCommand.LoginInfo login_info = 4;
  optional .wxassistantopenmsg.YBCommand.PosInfo pos_info = 5;
}

message TraceInfo {
  optional uint64 sendtime_ms = 1;
}

message PostMessageToYBReq {
  optional uint32 scene = 1;
  optional .wxassistantopenmsg.Message msg = 2;
  optional .wxassistantopenmsg.YBCommand cmd = 3;
  optional string openid = 4;
  optional string appid = 5;
  optional string token = 6;
  optional uint32 flag = 7;
  optional string biz_openid = 8;
  optional .wxassistantopenmsg.TraceInfo trace_info = 100;
}

message PostMessageToIntellReq {
  optional uint32 scene = 1;
  optional .wxassistantopenmsg.Message msg = 2;
  optional .wxassistantopenmsg.YBCommand cmd = 3;
  optional string openid = 4;
  optional string appid = 5;
  optional string token = 6;
  optional uint32 flag = 7;
  optional string biz_openid = 8;
}

message WxMsgRulePack {
  optional string ruleid = 1;
  optional bytes msgpack = 2;
  optional uint64 sendtime = 3;
}

