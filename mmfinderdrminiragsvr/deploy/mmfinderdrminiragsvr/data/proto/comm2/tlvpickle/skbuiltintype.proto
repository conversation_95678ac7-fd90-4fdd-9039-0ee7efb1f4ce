syntax = "proto2";

import "google/protobuf/descriptor.proto";
package tlvpickle;

option java_package = "com.tencent.tlvpickle";
option java_outer_classname = "tlvpickle";

enum MMClientCallType {
  kMMClientCallNormal = 0;
  kMMClientCallStrict = 1;
}

enum SvrkitProcessType {
  kSvrkitProcessNormal = 0;
  kSvrkitProcessAsync = 1;
}

enum MCPPrimitiveType {
  UNSPECIFIED = 0;
  TOOL = 1;
  RESOURCE = 2;
  PROMPT = 3;
}

message SKBuiltinInt32_PB {
  required uint32 iVal = 1;
}

message SKBuiltinUint32_PB {
  required uint32 uiVal = 1;
}

message SKBuiltinChar_PB {
  required int32 iVal = 1;
}

message SKBuiltinUchar_PB {
  required uint32 uiVal = 1;
}

message SKBuiltinInt8_PB {
  required int32 iVal = 1;
}

message SKBuiltinUint8_PB {
  required uint32 uiVal = 1;
}

message SKBuiltinInt16_PB {
  required int32 iVal = 1;
}

message SKBuiltinUint16_PB {
  required uint32 uiVal = 1;
}

message SKBuiltinInt64_PB {
  required int64 llVal = 1;
}

message SKBuiltinUint64_PB {
  required uint64 ullVal = 1;
}

message SKBuiltinFloat32_PB {
  required float fVal = 1;
}

message SKBuiltinDouble64_PB {
  required double dVal = 1;
}

message SKBuiltinBuffer_PB {
  required uint32 iLen = 1;
  optional bytes Buffer = 2;
}

message SKBuiltinString_PB {
  optional string String = 1;
}

message SKBuiltinBytes_PB {
  optional bytes Buffer = 1;
}

message SKBuiltinEmpty_PB {
}

message SKBuiltinNoexsit_PB {
}

message SKBuiltinEchoInfo_PB {
  required int32 EchoLen = 1;
  required bytes EchoStr = 2;
}

message SKPBMetaInfo {
  optional string file_name = 1;
  optional .tlvpickle.SKBuiltinBuffer_PB buffer = 2;
}

message SKPBMetaInfoResp {
  repeated .tlvpickle.SKPBMetaInfo info = 1;
}

message SKProfileReq {
  optional int32 index = 1;
  optional int32 oper = 2;
  optional int32 profile_type = 3;
  optional int32 timeout = 4;
}

message SKProfileResp {
  optional string result = 1;
}

message SKBuiltinGenericMsg_PB {
}

message SKByPassInfo {
  required int32 open = 1;
  required int32 percentage = 2;
  required string topic = 3;
  optional string key = 4;
  required string cluster = 5;
  repeated int32 skip_cmdids = 6;
  repeated uint32 uins = 7;
  optional uint32 max_pkg_size = 8 [default = 4194304];
}

message SKReport2WQueueReq {
  optional string topic = 2;
  optional bytes key = 3;
  optional bytes value = 4;
}

message SKExtendRet {
  required int32 ReadTimeout = 1;
  required int32 FastFail = 2;
  required int32 TicketErr = 3;
  required int32 OtherErr = 4;
}

message SKFieldMap {
  optional string ReqText = 1;
  optional string ReqTextWx = 2;
  optional string RetText = 3;
}

message ResourceOptions {
  optional string uri_template = 1;
  optional string mime_type = 2;
}

message PromptOptions {
}

message ToolOptions {
}

message MCPRule {
  optional string description = 2;
  optional .tlvpickle.MCPPrimitiveType primitive_type = 3;
  repeated string accept_groups = 4;
  repeated string block_groups = 5;
}

message MCPFieldOptions {
  optional bool required = 1;
  optional string description = 2;
  optional string title = 3;
  optional string default_value = 4;
  repeated string enum_values = 5;
  optional int32 max_length = 6;
  optional int32 min_length = 7;
  optional string pattern = 8;
  optional double max_value = 9;
  optional double min_value = 10;
  optional double multiple_of = 11;
  optional int32 min_items = 12;
  optional int32 max_items = 13;
  optional bool unique_items = 14;
  optional bool hidden = 15;
}

extend .google.protobuf.EnumValueOptions {
  optional string EnumValueDesc = 1000000;
}

extend .google.protobuf.EnumOptions {
  optional string EnumDesc = 1000000;
}

extend .google.protobuf.FieldOptions {
  optional string Desc = 1000010;
  optional uint64 Min = 1000011;
  optional uint64 Max = 1000012;
  optional string ValueList = 1000013;
  optional string SectionName = 1000014;
  optional string KeyName = 1000015;
  optional string WxUin64Type = 1000017;
  optional .tlvpickle.MCPFieldOptions mcp_options = 1000018;
}

extend .google.protobuf.ServiceOptions {
  optional string ServerType = 1000000;
  optional int32 Magic = 1000001;
  optional int32 Target = 1000002;
  optional string RouteMethod = 1000003;
  optional int32 UseGateway = 1000004;
  optional string TemplateType = 1000005;
  optional int32 MQType = 1000006;
  optional int32 UseWxPayMultiSet = 1000007;
  optional int32 EnableAttachment = 1000008;
  repeated string ClientAllowDepends = 1000009;
  optional int32 UseMultienv = 1000010;
  optional string ServiceRouteMethod = 1000011;
  optional string UinType = 1000012;
  optional string ServerUinType = 1000013;
  optional string ClientUinType = 1000014;
  optional int32 ServiceEnableMessageHook = 1000015;
}

extend .google.protobuf.MethodOptions {
  optional int32 CmdID = 1000000;
  optional string OptString = 1000001;
  optional string Usage = 1000002;
  optional int32 NeedExtEnd = 1000003;
  optional .tlvpickle.SKExtendRet ExtendRet = 1000004;
  optional string Brief = 1000005;
  optional string Url = 1000006;
  optional .tlvpickle.SKFieldMap FieldMap = 1000007;
  optional int32 RespType = 1000008;
  optional .tlvpickle.MMClientCallType ClientCallType = 1000009;
  optional int32 MQHandlerType = 1000010;
  optional string BusinessErrCode = 1000011;
  optional bool EnableReqValidator = 1000012;
  optional .tlvpickle.SvrkitProcessType ProcessType = 1000013;
  optional int32 IsBatchMethod = 1000014;
  optional string RpcRouteMethod = 1000015;
  optional int32 EnableMessageHook = 1000016;
  optional .tlvpickle.MCPRule mcp_rule = 1000017;
}

