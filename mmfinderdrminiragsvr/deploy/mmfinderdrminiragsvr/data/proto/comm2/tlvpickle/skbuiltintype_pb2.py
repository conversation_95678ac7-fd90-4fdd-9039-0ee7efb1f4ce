# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: comm2/tlvpickle/skbuiltintype.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import builder as _builder
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import descriptor_pb2 as google_dot_protobuf_dot_descriptor__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n#comm2/tlvpickle/skbuiltintype.proto\x12\ttlvpickle\x1a google/protobuf/descriptor.proto\"!\n\x11SKBuiltinInt32_PB\x12\x0c\n\x04iVal\x18\x01 \x02(\r\"#\n\x12SKBuiltinUint32_PB\x12\r\n\x05uiVal\x18\x01 \x02(\r\" \n\x10SKBuiltinChar_PB\x12\x0c\n\x04iVal\x18\x01 \x02(\x05\"\"\n\x11SKBuiltinUchar_PB\x12\r\n\x05uiVal\x18\x01 \x02(\r\" \n\x10SKBuiltinInt8_PB\x12\x0c\n\x04iVal\x18\x01 \x02(\x05\"\"\n\x11SKBuiltinUint8_PB\x12\r\n\x05uiVal\x18\x01 \x02(\r\"!\n\x11SKBuiltinInt16_PB\x12\x0c\n\x04iVal\x18\x01 \x02(\x05\"#\n\x12SKBuiltinUint16_PB\x12\r\n\x05uiVal\x18\x01 \x02(\r\"\"\n\x11SKBuiltinInt64_PB\x12\r\n\x05llVal\x18\x01 \x02(\x03\"$\n\x12SKBuiltinUint64_PB\x12\x0e\n\x06ullVal\x18\x01 \x02(\x04\"#\n\x13SKBuiltinFloat32_PB\x12\x0c\n\x04\x66Val\x18\x01 \x02(\x02\"$\n\x14SKBuiltinDouble64_PB\x12\x0c\n\x04\x64Val\x18\x01 \x02(\x01\"2\n\x12SKBuiltinBuffer_PB\x12\x0c\n\x04iLen\x18\x01 \x02(\r\x12\x0e\n\x06\x42uffer\x18\x02 \x01(\x0c\"$\n\x12SKBuiltinString_PB\x12\x0e\n\x06String\x18\x01 \x01(\t\"#\n\x11SKBuiltinBytes_PB\x12\x0e\n\x06\x42uffer\x18\x01 \x01(\x0c\"\x13\n\x11SKBuiltinEmpty_PB\"\x15\n\x13SKBuiltinNoexsit_PB\"8\n\x14SKBuiltinEchoInfo_PB\x12\x0f\n\x07\x45\x63hoLen\x18\x01 \x02(\x05\x12\x0f\n\x07\x45\x63hoStr\x18\x02 \x02(\x0c\"P\n\x0cSKPBMetaInfo\x12\x11\n\tfile_name\x18\x01 \x01(\t\x12-\n\x06\x62uffer\x18\x02 \x01(\x0b\x32\x1d.tlvpickle.SKBuiltinBuffer_PB\"9\n\x10SKPBMetaInfoResp\x12%\n\x04info\x18\x01 \x03(\x0b\x32\x17.tlvpickle.SKPBMetaInfo\"R\n\x0cSKProfileReq\x12\r\n\x05index\x18\x01 \x01(\x05\x12\x0c\n\x04oper\x18\x02 \x01(\x05\x12\x14\n\x0cprofile_type\x18\x03 \x01(\x05\x12\x0f\n\x07timeout\x18\x04 \x01(\x05\"\x1f\n\rSKProfileResp\x12\x0e\n\x06result\x18\x01 \x01(\t\"\x18\n\x16SKBuiltinGenericMsg_PB\"\x9f\x01\n\x0cSKByPassInfo\x12\x0c\n\x04open\x18\x01 \x02(\x05\x12\x12\n\npercentage\x18\x02 \x02(\x05\x12\r\n\x05topic\x18\x03 \x02(\t\x12\x0b\n\x03key\x18\x04 \x01(\t\x12\x0f\n\x07\x63luster\x18\x05 \x02(\t\x12\x13\n\x0bskip_cmdids\x18\x06 \x03(\x05\x12\x0c\n\x04uins\x18\x07 \x03(\r\x12\x1d\n\x0cmax_pkg_size\x18\x08 \x01(\r:\x07\x34\x31\x39\x34\x33\x30\x34\"?\n\x12SKReport2WQueueReq\x12\r\n\x05topic\x18\x02 \x01(\t\x12\x0b\n\x03key\x18\x03 \x01(\x0c\x12\r\n\x05value\x18\x04 \x01(\x0c\"Y\n\x0bSKExtendRet\x12\x13\n\x0bReadTimeout\x18\x01 \x02(\x05\x12\x10\n\x08\x46\x61stFail\x18\x02 \x02(\x05\x12\x11\n\tTicketErr\x18\x03 \x02(\x05\x12\x10\n\x08OtherErr\x18\x04 \x02(\x05\"A\n\nSKFieldMap\x12\x0f\n\x07ReqText\x18\x01 \x01(\t\x12\x11\n\tReqTextWx\x18\x02 \x01(\t\x12\x0f\n\x07RetText\x18\x03 \x01(\t\":\n\x0fResourceOptions\x12\x14\n\x0curi_template\x18\x01 \x01(\t\x12\x11\n\tmime_type\x18\x02 \x01(\t\"\x0f\n\rPromptOptions\"\r\n\x0bToolOptions\"\x80\x01\n\x07MCPRule\x12\x13\n\x0b\x64\x65scription\x18\x02 \x01(\t\x12\x33\n\x0eprimitive_type\x18\x03 \x01(\x0e\x32\x1b.tlvpickle.MCPPrimitiveType\x12\x15\n\raccept_groups\x18\x04 \x03(\t\x12\x14\n\x0c\x62lock_groups\x18\x05 \x03(\t\"\xb3\x02\n\x0fMCPFieldOptions\x12\x10\n\x08required\x18\x01 \x01(\x08\x12\x13\n\x0b\x64\x65scription\x18\x02 \x01(\t\x12\r\n\x05title\x18\x03 \x01(\t\x12\x15\n\rdefault_value\x18\x04 \x01(\t\x12\x13\n\x0b\x65num_values\x18\x05 \x03(\t\x12\x12\n\nmax_length\x18\x06 \x01(\x05\x12\x12\n\nmin_length\x18\x07 \x01(\x05\x12\x0f\n\x07pattern\x18\x08 \x01(\t\x12\x11\n\tmax_value\x18\t \x01(\x01\x12\x11\n\tmin_value\x18\n \x01(\x01\x12\x13\n\x0bmultiple_of\x18\x0b \x01(\x01\x12\x11\n\tmin_items\x18\x0c \x01(\x05\x12\x11\n\tmax_items\x18\r \x01(\x05\x12\x14\n\x0cunique_items\x18\x0e \x01(\x08\x12\x0e\n\x06hidden\x18\x0f \x01(\x08*D\n\x10MMClientCallType\x12\x17\n\x13kMMClientCallNormal\x10\x00\x12\x17\n\x13kMMClientCallStrict\x10\x01*F\n\x11SvrkitProcessType\x12\x18\n\x14kSvrkitProcessNormal\x10\x00\x12\x17\n\x13kSvrkitProcessAsync\x10\x01*G\n\x10MCPPrimitiveType\x12\x0f\n\x0bUNSPECIFIED\x10\x00\x12\x08\n\x04TOOL\x10\x01\x12\x0c\n\x08RESOURCE\x10\x02\x12\n\n\x06PROMPT\x10\x03::\n\rEnumValueDesc\x12!.google.protobuf.EnumValueOptions\x18\xc0\x84= \x01(\t:0\n\x08\x45numDesc\x12\x1c.google.protobuf.EnumOptions\x18\xc0\x84= \x01(\t:-\n\x04\x44\x65sc\x12\x1d.google.protobuf.FieldOptions\x18\xca\x84= \x01(\t:,\n\x03Min\x12\x1d.google.protobuf.FieldOptions\x18\xcb\x84= \x01(\x04:,\n\x03Max\x12\x1d.google.protobuf.FieldOptions\x18\xcc\x84= \x01(\x04:2\n\tValueList\x12\x1d.google.protobuf.FieldOptions\x18\xcd\x84= \x01(\t:4\n\x0bSectionName\x12\x1d.google.protobuf.FieldOptions\x18\xce\x84= \x01(\t:0\n\x07KeyName\x12\x1d.google.protobuf.FieldOptions\x18\xcf\x84= \x01(\t:4\n\x0bWxUin64Type\x12\x1d.google.protobuf.FieldOptions\x18\xd1\x84= \x01(\t:P\n\x0bmcp_options\x12\x1d.google.protobuf.FieldOptions\x18\xd2\x84= \x01(\x0b\x32\x1a.tlvpickle.MCPFieldOptions:5\n\nServerType\x12\x1f.google.protobuf.ServiceOptions\x18\xc0\x84= \x01(\t:0\n\x05Magic\x12\x1f.google.protobuf.ServiceOptions\x18\xc1\x84= \x01(\x05:1\n\x06Target\x12\x1f.google.protobuf.ServiceOptions\x18\xc2\x84= \x01(\x05:6\n\x0bRouteMethod\x12\x1f.google.protobuf.ServiceOptions\x18\xc3\x84= \x01(\t:5\n\nUseGateway\x12\x1f.google.protobuf.ServiceOptions\x18\xc4\x84= \x01(\x05:7\n\x0cTemplateType\x12\x1f.google.protobuf.ServiceOptions\x18\xc5\x84= \x01(\t:1\n\x06MQType\x12\x1f.google.protobuf.ServiceOptions\x18\xc6\x84= \x01(\x05:;\n\x10UseWxPayMultiSet\x12\x1f.google.protobuf.ServiceOptions\x18\xc7\x84= \x01(\x05:;\n\x10\x45nableAttachment\x12\x1f.google.protobuf.ServiceOptions\x18\xc8\x84= \x01(\x05:=\n\x12\x43lientAllowDepends\x12\x1f.google.protobuf.ServiceOptions\x18\xc9\x84= \x03(\t:6\n\x0bUseMultienv\x12\x1f.google.protobuf.ServiceOptions\x18\xca\x84= \x01(\x05:=\n\x12ServiceRouteMethod\x12\x1f.google.protobuf.ServiceOptions\x18\xcb\x84= \x01(\t:2\n\x07UinType\x12\x1f.google.protobuf.ServiceOptions\x18\xcc\x84= \x01(\t:8\n\rServerUinType\x12\x1f.google.protobuf.ServiceOptions\x18\xcd\x84= \x01(\t:8\n\rClientUinType\x12\x1f.google.protobuf.ServiceOptions\x18\xce\x84= \x01(\t:C\n\x18ServiceEnableMessageHook\x12\x1f.google.protobuf.ServiceOptions\x18\xcf\x84= \x01(\x05:/\n\x05\x43mdID\x12\x1e.google.protobuf.MethodOptions\x18\xc0\x84= \x01(\x05:3\n\tOptString\x12\x1e.google.protobuf.MethodOptions\x18\xc1\x84= \x01(\t:/\n\x05Usage\x12\x1e.google.protobuf.MethodOptions\x18\xc2\x84= \x01(\t:4\n\nNeedExtEnd\x12\x1e.google.protobuf.MethodOptions\x18\xc3\x84= \x01(\x05:K\n\tExtendRet\x12\x1e.google.protobuf.MethodOptions\x18\xc4\x84= \x01(\x0b\x32\x16.tlvpickle.SKExtendRet:/\n\x05\x42rief\x12\x1e.google.protobuf.MethodOptions\x18\xc5\x84= \x01(\t:-\n\x03Url\x12\x1e.google.protobuf.MethodOptions\x18\xc6\x84= \x01(\t:I\n\x08\x46ieldMap\x12\x1e.google.protobuf.MethodOptions\x18\xc7\x84= \x01(\x0b\x32\x15.tlvpickle.SKFieldMap:2\n\x08RespType\x12\x1e.google.protobuf.MethodOptions\x18\xc8\x84= \x01(\x05:U\n\x0e\x43lientCallType\x12\x1e.google.protobuf.MethodOptions\x18\xc9\x84= \x01(\x0e\x32\x1b.tlvpickle.MMClientCallType:7\n\rMQHandlerType\x12\x1e.google.protobuf.MethodOptions\x18\xca\x84= \x01(\x05:9\n\x0f\x42usinessErrCode\x12\x1e.google.protobuf.MethodOptions\x18\xcb\x84= \x01(\t:<\n\x12\x45nableReqValidator\x12\x1e.google.protobuf.MethodOptions\x18\xcc\x84= \x01(\x08:S\n\x0bProcessType\x12\x1e.google.protobuf.MethodOptions\x18\xcd\x84= \x01(\x0e\x32\x1c.tlvpickle.SvrkitProcessType:7\n\rIsBatchMethod\x12\x1e.google.protobuf.MethodOptions\x18\xce\x84= \x01(\x05:8\n\x0eRpcRouteMethod\x12\x1e.google.protobuf.MethodOptions\x18\xcf\x84= \x01(\t:;\n\x11\x45nableMessageHook\x12\x1e.google.protobuf.MethodOptions\x18\xd0\x84= \x01(\x05:F\n\x08mcp_rule\x12\x1e.google.protobuf.MethodOptions\x18\xd1\x84= \x01(\x0b\x32\x12.tlvpickle.MCPRuleB\"\n\x15\x63om.tencent.tlvpickleB\ttlvpickle')

_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, globals())
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'comm2.tlvpickle.skbuiltintype_pb2', globals())
if _descriptor._USE_C_DESCRIPTORS == False:
  google_dot_protobuf_dot_descriptor__pb2.EnumValueOptions.RegisterExtension(EnumValueDesc)
  google_dot_protobuf_dot_descriptor__pb2.EnumOptions.RegisterExtension(EnumDesc)
  google_dot_protobuf_dot_descriptor__pb2.FieldOptions.RegisterExtension(Desc)
  google_dot_protobuf_dot_descriptor__pb2.FieldOptions.RegisterExtension(Min)
  google_dot_protobuf_dot_descriptor__pb2.FieldOptions.RegisterExtension(Max)
  google_dot_protobuf_dot_descriptor__pb2.FieldOptions.RegisterExtension(ValueList)
  google_dot_protobuf_dot_descriptor__pb2.FieldOptions.RegisterExtension(SectionName)
  google_dot_protobuf_dot_descriptor__pb2.FieldOptions.RegisterExtension(KeyName)
  google_dot_protobuf_dot_descriptor__pb2.FieldOptions.RegisterExtension(WxUin64Type)
  google_dot_protobuf_dot_descriptor__pb2.FieldOptions.RegisterExtension(mcp_options)
  google_dot_protobuf_dot_descriptor__pb2.ServiceOptions.RegisterExtension(ServerType)
  google_dot_protobuf_dot_descriptor__pb2.ServiceOptions.RegisterExtension(Magic)
  google_dot_protobuf_dot_descriptor__pb2.ServiceOptions.RegisterExtension(Target)
  google_dot_protobuf_dot_descriptor__pb2.ServiceOptions.RegisterExtension(RouteMethod)
  google_dot_protobuf_dot_descriptor__pb2.ServiceOptions.RegisterExtension(UseGateway)
  google_dot_protobuf_dot_descriptor__pb2.ServiceOptions.RegisterExtension(TemplateType)
  google_dot_protobuf_dot_descriptor__pb2.ServiceOptions.RegisterExtension(MQType)
  google_dot_protobuf_dot_descriptor__pb2.ServiceOptions.RegisterExtension(UseWxPayMultiSet)
  google_dot_protobuf_dot_descriptor__pb2.ServiceOptions.RegisterExtension(EnableAttachment)
  google_dot_protobuf_dot_descriptor__pb2.ServiceOptions.RegisterExtension(ClientAllowDepends)
  google_dot_protobuf_dot_descriptor__pb2.ServiceOptions.RegisterExtension(UseMultienv)
  google_dot_protobuf_dot_descriptor__pb2.ServiceOptions.RegisterExtension(ServiceRouteMethod)
  google_dot_protobuf_dot_descriptor__pb2.ServiceOptions.RegisterExtension(UinType)
  google_dot_protobuf_dot_descriptor__pb2.ServiceOptions.RegisterExtension(ServerUinType)
  google_dot_protobuf_dot_descriptor__pb2.ServiceOptions.RegisterExtension(ClientUinType)
  google_dot_protobuf_dot_descriptor__pb2.ServiceOptions.RegisterExtension(ServiceEnableMessageHook)
  google_dot_protobuf_dot_descriptor__pb2.MethodOptions.RegisterExtension(CmdID)
  google_dot_protobuf_dot_descriptor__pb2.MethodOptions.RegisterExtension(OptString)
  google_dot_protobuf_dot_descriptor__pb2.MethodOptions.RegisterExtension(Usage)
  google_dot_protobuf_dot_descriptor__pb2.MethodOptions.RegisterExtension(NeedExtEnd)
  google_dot_protobuf_dot_descriptor__pb2.MethodOptions.RegisterExtension(ExtendRet)
  google_dot_protobuf_dot_descriptor__pb2.MethodOptions.RegisterExtension(Brief)
  google_dot_protobuf_dot_descriptor__pb2.MethodOptions.RegisterExtension(Url)
  google_dot_protobuf_dot_descriptor__pb2.MethodOptions.RegisterExtension(FieldMap)
  google_dot_protobuf_dot_descriptor__pb2.MethodOptions.RegisterExtension(RespType)
  google_dot_protobuf_dot_descriptor__pb2.MethodOptions.RegisterExtension(ClientCallType)
  google_dot_protobuf_dot_descriptor__pb2.MethodOptions.RegisterExtension(MQHandlerType)
  google_dot_protobuf_dot_descriptor__pb2.MethodOptions.RegisterExtension(BusinessErrCode)
  google_dot_protobuf_dot_descriptor__pb2.MethodOptions.RegisterExtension(EnableReqValidator)
  google_dot_protobuf_dot_descriptor__pb2.MethodOptions.RegisterExtension(ProcessType)
  google_dot_protobuf_dot_descriptor__pb2.MethodOptions.RegisterExtension(IsBatchMethod)
  google_dot_protobuf_dot_descriptor__pb2.MethodOptions.RegisterExtension(RpcRouteMethod)
  google_dot_protobuf_dot_descriptor__pb2.MethodOptions.RegisterExtension(EnableMessageHook)
  google_dot_protobuf_dot_descriptor__pb2.MethodOptions.RegisterExtension(mcp_rule)

  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n\025com.tencent.tlvpickleB\ttlvpickle'
  _MMCLIENTCALLTYPE._serialized_start=1948
  _MMCLIENTCALLTYPE._serialized_end=2016
  _SVRKITPROCESSTYPE._serialized_start=2018
  _SVRKITPROCESSTYPE._serialized_end=2088
  _MCPPRIMITIVETYPE._serialized_start=2090
  _MCPPRIMITIVETYPE._serialized_end=2161
  _SKBUILTININT32_PB._serialized_start=84
  _SKBUILTININT32_PB._serialized_end=117
  _SKBUILTINUINT32_PB._serialized_start=119
  _SKBUILTINUINT32_PB._serialized_end=154
  _SKBUILTINCHAR_PB._serialized_start=156
  _SKBUILTINCHAR_PB._serialized_end=188
  _SKBUILTINUCHAR_PB._serialized_start=190
  _SKBUILTINUCHAR_PB._serialized_end=224
  _SKBUILTININT8_PB._serialized_start=226
  _SKBUILTININT8_PB._serialized_end=258
  _SKBUILTINUINT8_PB._serialized_start=260
  _SKBUILTINUINT8_PB._serialized_end=294
  _SKBUILTININT16_PB._serialized_start=296
  _SKBUILTININT16_PB._serialized_end=329
  _SKBUILTINUINT16_PB._serialized_start=331
  _SKBUILTINUINT16_PB._serialized_end=366
  _SKBUILTININT64_PB._serialized_start=368
  _SKBUILTININT64_PB._serialized_end=402
  _SKBUILTINUINT64_PB._serialized_start=404
  _SKBUILTINUINT64_PB._serialized_end=440
  _SKBUILTINFLOAT32_PB._serialized_start=442
  _SKBUILTINFLOAT32_PB._serialized_end=477
  _SKBUILTINDOUBLE64_PB._serialized_start=479
  _SKBUILTINDOUBLE64_PB._serialized_end=515
  _SKBUILTINBUFFER_PB._serialized_start=517
  _SKBUILTINBUFFER_PB._serialized_end=567
  _SKBUILTINSTRING_PB._serialized_start=569
  _SKBUILTINSTRING_PB._serialized_end=605
  _SKBUILTINBYTES_PB._serialized_start=607
  _SKBUILTINBYTES_PB._serialized_end=642
  _SKBUILTINEMPTY_PB._serialized_start=644
  _SKBUILTINEMPTY_PB._serialized_end=663
  _SKBUILTINNOEXSIT_PB._serialized_start=665
  _SKBUILTINNOEXSIT_PB._serialized_end=686
  _SKBUILTINECHOINFO_PB._serialized_start=688
  _SKBUILTINECHOINFO_PB._serialized_end=744
  _SKPBMETAINFO._serialized_start=746
  _SKPBMETAINFO._serialized_end=826
  _SKPBMETAINFORESP._serialized_start=828
  _SKPBMETAINFORESP._serialized_end=885
  _SKPROFILEREQ._serialized_start=887
  _SKPROFILEREQ._serialized_end=969
  _SKPROFILERESP._serialized_start=971
  _SKPROFILERESP._serialized_end=1002
  _SKBUILTINGENERICMSG_PB._serialized_start=1004
  _SKBUILTINGENERICMSG_PB._serialized_end=1028
  _SKBYPASSINFO._serialized_start=1031
  _SKBYPASSINFO._serialized_end=1190
  _SKREPORT2WQUEUEREQ._serialized_start=1192
  _SKREPORT2WQUEUEREQ._serialized_end=1255
  _SKEXTENDRET._serialized_start=1257
  _SKEXTENDRET._serialized_end=1346
  _SKFIELDMAP._serialized_start=1348
  _SKFIELDMAP._serialized_end=1413
  _RESOURCEOPTIONS._serialized_start=1415
  _RESOURCEOPTIONS._serialized_end=1473
  _PROMPTOPTIONS._serialized_start=1475
  _PROMPTOPTIONS._serialized_end=1490
  _TOOLOPTIONS._serialized_start=1492
  _TOOLOPTIONS._serialized_end=1505
  _MCPRULE._serialized_start=1508
  _MCPRULE._serialized_end=1636
  _MCPFIELDOPTIONS._serialized_start=1639
  _MCPFIELDOPTIONS._serialized_end=1946
# @@protoc_insertion_point(module_scope)
