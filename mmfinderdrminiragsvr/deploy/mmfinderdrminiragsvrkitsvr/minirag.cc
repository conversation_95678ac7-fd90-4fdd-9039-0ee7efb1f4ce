#include <error.h>
#include <stdint.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>

#include <openssl/md5.h>
#include <pybind11/pybind11.h>
#include <chrono>
#include <functional>
#include <iomanip>
#include <map>
#include <string>
#include <unordered_map>
#include <unordered_set>
#include <utility>
#include <vector>
#include "comm2/svrkit/skclosuretimeout.h"
#include "dify.hh"
#include "iLogInternal.h"
#include "mmfddatagateway/mmfddataubf/mmfddataubf_utils.h"
#include "mmfddatagateway/mmfddataubfsvr/mmfddataubfsvrreqhelper.h"
#include "mmfddataubf_def.h"
#include "mmfddataubfsvrclient.h"
#include "prservice/mmspropvpsvr_gpu/mmspropvpsvrclient.h"
// # biz
#include "comm2/core/encoding/iBase64.h"
#include "mmbizgateway/mmbizgwutils/mmbizwxaaiagententrance/mmbizwxaaiagententranceclient.h"

pybind11::dict get_ubf(uint64_t uin) {
    pybind11::dict item;
    MMFDDataUBFSvrClient ubfClient;
    mmfddataubf::UBFRequest ubfReq;
    ubfReq.set_access_key("t26B6Xr4");
    ubfReq.set_uin(uin);
    mmfddataubf::UBFResponse ubfResp;
    int ubfRet = ubfClient.GetUserBaseFeature(uin, ubfReq, ubfResp);
    item["ubf_ret"] = ubfRet;
    item["ubf_resp"] = ubfResp.DebugString();
    return item;
}

pybind11::dict get_wx_app_usage_record(uint64_t uin) {
    pybind11::dict item;
    MMBizWxaAiAgentEntranceClient client;
    mmbizwxaaiagententrance::GetWxaUsageRecordReq req;
    mmbizwxaaiagententrance::GetWxaUsageRecordResp resp;
    req.set_useruin(uin);
    req.set_condition(7);
//     req.set_scene(2);
    req.set_scene(mmbizwxaaiagententrance::enWhiteListScene::AI_AGENT_ENTRANCE_SCENE);


    int ret = client.GetWxaUsageRecord(Comm::GetSysReqCookie()->uin(), req, resp);
//     int ret = client.GetWxaUsageRecord(uin, req, resp);
    item["ret"] = ret;
    item["resp"] = resp.DebugString();
    std::cout << "[get_wx_app_usage_record] resp:" << resp.DebugString() << std::endl;
    // resp to json
    pybind11::list commuse_list;
    for (const auto& record : resp.commuse_list()) {
        pybind11::dict record_item;
        record_item["appid"] = record.appid();
        record_item["nickname"] = record.nickname();
        record_item["version_type"] = record.version_type();
        record_item["update_time"] = record.update_time();
        record_item["create_time"] = record.create_time();
        record_item["friends_used_num"] = record.friends_used_num();
        commuse_list.append(record_item);
    }
    item["commuse_list"] = commuse_list;

    pybind11::list history_list;
    for (const auto& record : resp.history_list()) {
        pybind11::dict record_item;
        record_item["appid"] = record.appid();
        record_item["nickname"] = record.nickname();
        record_item["version_type"] = record.version_type();
        record_item["update_time"] = record.update_time();
        record_item["create_time"] = record.create_time();
        record_item["friends_used_num"] = record.friends_used_num();
        history_list.append(record_item);
    }
    item["history_list"] = history_list;

    pybind11::list star_list;
    for (const auto& record : resp.star_list()) {
        pybind11::dict record_item;
        record_item["appid"] = record.appid();
        record_item["nickname"] = record.nickname();
        record_item["version_type"] = record.version_type();
        record_item["update_time"] = record.update_time();
        record_item["create_time"] = record.create_time();
        record_item["friends_used_num"] = record.friends_used_num();
        star_list.append(record_item);
    }
    item["star_list"] = star_list;
    return item;
}

pybind11::dict get_wx_app_feature(uint64_t uin, std::vector<std::string> appid_list) {
    pybind11::dict item;
    MMBizWxaAiAgentEntranceClient client;
    mmbizwxaaiagententrance::GetWxaFeatureReq req;
    mmbizwxaaiagententrance::GetWxaFeatureResp resp;
    req.set_useruin(uin);
    for (int i = 0; i < int(appid_list.size()); ++i) {
        req.add_appid_list(appid_list[i]);
    }
    //     req.set_scene(2);
    req.set_scene(mmbizwxaaiagententrance::enWhiteListScene::AI_AGENT_ENTRANCE_SCENE);

    int ret = client.GetWxaFeature(Comm::GetSysReqCookie()->uin(), req, resp);
//     int ret = client.GetWxaFeature(uin, req, resp);
    item["ret"] = ret;
    item["resp"] = resp.DebugString();
    std::cout << "[get_wx_app_feature] resp:" << resp.DebugString() << std::endl;
    pybind11::list feature_list;
    for (const auto& feature : resp.wxa_feature_list()) {
        pybind11::dict feature_item;
        feature_item["appid"] = feature.appid();
        feature_item["nickname"] = feature.nickname();
        feature_item["friends_used_num"] = feature.friends_used_num();
        feature_list.append(feature_item);
    }
    item["wxa_feature_list"] = feature_list;
    return item;
}

PYBIND11_MODULE(minirag, m) {
    m.def("get_ubf", &get_ubf);
    m.def("get_wx_app_usage_record", &get_wx_app_usage_record);
    m.def("get_wx_app_feature", &get_wx_app_feature);
    pybind11::class_<dify::PyToolKv>(m, "PyToolKv")
        .def(pybind11::init())
        .def("get", &dify::PyToolKv::get, "get key")
        .def("set", &dify::PyToolKv::set, pybind11::arg("key"), pybind11::arg("val"),
             pybind11::arg("expire_timestamp") = 0, "set key-val")
        .def("clear", &dify::PyToolKv::clear, "clear key");

    pybind11::class_<dify::PyTableKv>(m, "PyTableKv")
        .def(pybind11::init())
        .def("trans", &dify::PyTableKv::Transaction, pybind11::arg("use_tso"),
             pybind11::arg("auto_commit"), "begin transaction")
        .def("commit", &dify::PyTableKv::Commit, pybind11::arg("committed") = true, "trans commit")
        .def("rollback", &dify::PyTableKv::Rollback, "trans rollback")
        .def("query", &dify::PyTableKv::Query, pybind11::arg("uin"), pybind11::arg("sql"),
             pybind11::arg("result"), pybind11::arg("opts"), "sql query");
}
