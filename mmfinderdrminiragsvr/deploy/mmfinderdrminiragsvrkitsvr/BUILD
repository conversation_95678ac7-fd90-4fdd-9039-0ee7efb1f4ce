#
# dify so
# author: huaideliu copy from nickwu
# date: 2025-07-21
#

cc_binary(
  name = "libdify.so",
  srcs = [
    "kv.cc",
    "tablekv.cc",
    "dify.cc"
  ],
  includes = ['.'],
  deps = [
    "//oss/mmlas:mmlas",
    '//mm3rd/pybind11:pybind11_py3',
    '//weixin/mmfinderluban/mmassistant/mmfinderdrassistantsvr/tool_kv:tool_kv',
    '//weixin/mmfinderluban/mmassistant/mmfinderdrassistantsvr:mmfinderdrassistantsvr_comm',
    "//platform/kv6:newkv6svrclient",
    "//kvstore/kvsvr:table5client",
  ],
  copts = [
    "-I.",
    "-std=c++17",
    "-fPIC",
  ],
  linkopts = [
    "-shared",
  ],
  visibility = ["//visibility:public"],
  linkshared=True,
)

cc_binary(
  name = "minirag.so",
  srcs = [
    "kv.cc",
    "minirag.cc",
    "tablekv.cc",
  ],
  includes = ['.'],
  deps = [
    "//oss/mmlas:mmlas",
    "//mm3rd/openssl:ssl",
    '//mm3rd/pybind11:pybind11_py3',
    '//weixin/mmfinderluban/mmassistant/mmfinderdrassistantsvr/tool_kv:tool_kv',
    '//weixin/mmfinderluban/mmassistant/mmfinderdrassistantsvr:mmfinderdrassistantsvr_comm',
    "//platform/kv6:newkv6svrclient",
    "//kvstore/kvsvr:table5client",
    "//prservice/mmspropvpsvr_gpu:mmspropvpsvrclient",
    '//mmfddatagateway/mmfddataubfsvr:mmfddataubfsvrreqhelper',
    "//mmbizgateway/mmbizgwutils/mmbizwxaaiagententrance:mmbizwxaaiagententranceclient",
  ],
  copts = [
    "-I.",
    "-std=c++17",
    "-fPIC",
  ],
  linkopts = [
    "-shared",
  ],
  visibility = ["//visibility:public"],
  linkshared=True,
)