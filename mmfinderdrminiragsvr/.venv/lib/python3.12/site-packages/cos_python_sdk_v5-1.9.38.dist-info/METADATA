Metadata-Version: 2.4
Name: cos_python_sdk_v5
Version: 1.9.38
Summary: cos-python-sdk-v5
Home-page: https://www.qcloud.com/
Author: tiedu, lewzylu, channing<PERSON>u
Author-email: <EMAIL>
License: MIT
License-File: LICENSE
Requires-Dist: requests>=2.8
Requires-Dist: xmltodict
Requires-Dist: six
Requires-Dist: crcmod
Requires-Dist: pycryptodome
Dynamic: author
Dynamic: author-email
Dynamic: description
Dynamic: home-page
Dynamic: license
Dynamic: license-file
Dynamic: requires-dist
Dynamic: summary

Qcloud COSv5 SDK
#######################

.. image:: https://img.shields.io/pypi/v/cos-python-sdk-v5.svg
   :target: https://pypi.org/search/?q=cos-python-sdk-v5
   :alt: Pypi
.. image:: https://api.travis-ci.com/tencentyun/cos-python-sdk-v5.svg?branch=master
   :target: https://app.travis-ci.com/github/tencentyun/cos-python-sdk-v5
   :alt: Travis CI 

介绍
_______

腾讯云COSV5Python SDK, 目前可以支持Python2.6与Python2.7以及Python3.x。

安装指南
__________

使用pip安装 ::

    pip install -U cos-python-sdk-v5

手动安装::

    python setup.py install

使用方法
__________

使用python sdk，参照 https://github.com/tencentyun/cos-python-sdk-v5/blob/master/demo/demo.py

cos最新可用地域，参照 https://cloud.tencent.com/document/product/436/6224

python sdk 快速入门，参照 https://cloud.tencent.com/document/product/436/12269

python sdk 接口文档，参照 https://cloud.tencent.com/document/product/436/12270
